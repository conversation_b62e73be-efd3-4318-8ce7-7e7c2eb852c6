{"name": "contract", "version": "0.1.0", "description": "UNCHAIN ETH NFT Maker contract", "private": true, "scripts": {"run:script": "npx hardhat run scripts/run.js", "deploy": "npx hardhat run scripts/deploy.js --network sepolia", "deploy:local": "npx hardhat run scripts/deploy.js --network localhost", "test": "npx hardhat test", "compile": "npx hardhat compile", "verify": "npx hardhat verify --network sepolia", "node": "npx hardhat node"}, "devDependencies": {"@ethersproject/providers": "^5.7.2", "@nomicfoundation/hardhat-chai-matchers": "^2.0.7", "@nomicfoundation/hardhat-ethers": "^3.0.9", "@nomicfoundation/hardhat-ignition": "^0.15.11", "@nomicfoundation/hardhat-ignition-ethers": "^0.15.12", "@nomicfoundation/hardhat-network-helpers": "^1.0.11", "@nomicfoundation/hardhat-toolbox": "^5.0.0", "@nomicfoundation/hardhat-verify": "^2.0.8", "@nomicfoundation/ignition-core": "^0.15.11", "@typechain/ethers-v6": "^0.5.1", "@typechain/hardhat": "^9.1.0", "@types/mocha": "^10.0.10", "@types/node": "^22.15.30", "chai": "^5.2.0", "dotenv": "^16.4.5", "ethers": "^6.14.3", "hardhat": "^2.22.5", "hardhat-gas-reporter": "^1.0.10", "mocha": "^11.6.0", "solidity-coverage": "^0.8.12", "ts-node": "^10.9.2", "typechain": "^8.3.2", "typescript": "^5.8.3"}, "dependencies": {"@openzeppelin/contracts": "^5.3.0"}}