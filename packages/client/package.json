{"name": "client", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.15.15", "@mui/material": "^5.15.15", "@mui/styled-engine-sc": "^6.0.0-alpha.18", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@testing-library/user-event": "^14.5.2", "@web3-storage/w3up-client": "^16.0.0", "ethers": "^6.11.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-scripts": "5.0.1", "styled-components": "^6.1.8", "web-vitals": "^3.5.2"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}