import React from 'react';\nimport { Box, CircularProgress, Typography, Fade } from '@mui/material';\n\n/**\n * React 19 Suspense対応 ローディングコンポーネント\n * NFTアプリ専用のローディングUI\n */\nconst NFTLoadingSpinner = ({ \n  message = 'Loading...', \n  size = 60, \n  showMessage = true,\n  fullScreen = false \n}) => {\n  const LoadingContent = () => (\n    <Fade in timeout={300}>\n      <Box\n        display=\"flex\"\n        flexDirection=\"column\"\n        alignItems=\"center\"\n        justifyContent=\"center\"\n        gap={2}\n        p={3}\n      >\n        <Box position=\"relative\">\n          <CircularProgress \n            size={size} \n            thickness={4}\n            style={{ color: '#1976d2' }}\n          />\n          <Box\n            position=\"absolute\"\n            top={0}\n            left={0}\n            bottom={0}\n            right={0}\n            display=\"flex\"\n            alignItems=\"center\"\n            justifyContent=\"center\"\n          >\n            <Typography\n              variant=\"caption\"\n              component=\"div\"\n              style={{ fontSize: '1.5rem' }}\n            >\n              🖼️\n            </Typography>\n          </Box>\n        </Box>\n        \n        {showMessage && (\n          <Typography \n            variant=\"h6\" \n            color=\"textSecondary\"\n            style={{ \n              fontWeight: 500,\n              animation: 'pulse 2s infinite'\n            }}\n          >\n            {message}\n          </Typography>\n        )}\n        \n        <Typography \n          variant=\"body2\" \n          color=\"textSecondary\"\n          style={{ opacity: 0.7 }}\n        >\n          NFTの準備をしています...\n        </Typography>\n        \n        <style jsx>{`\n          @keyframes pulse {\n            0% { opacity: 1; }\n            50% { opacity: 0.5; }\n            100% { opacity: 1; }\n          }\n        `}</style>\n      </Box>\n    </Fade>\n  );\n\n  if (fullScreen) {\n    return (\n      <Box\n        position=\"fixed\"\n        top={0}\n        left={0}\n        right={0}\n        bottom={0}\n        bgcolor=\"rgba(255, 255, 255, 0.9)\"\n        display=\"flex\"\n        alignItems=\"center\"\n        justifyContent=\"center\"\n        zIndex={9999}\n        style={{ backdropFilter: 'blur(4px)' }}\n      >\n        <LoadingContent />\n      </Box>\n    );\n  }\n\n  return (\n    <Box\n      minHeight=\"200px\"\n      display=\"flex\"\n      alignItems=\"center\"\n      justifyContent=\"center\"\n    >\n      <LoadingContent />\n    </Box>\n  );\n};\n\n// 特定用途のローディングコンポーネント\nexport const WalletLoadingSpinner = () => (\n  <NFTLoadingSpinner \n    message=\"ウォレットに接続中...\" \n    size={50}\n  />\n);\n\nexport const ContractLoadingSpinner = () => (\n  <NFTLoadingSpinner \n    message=\"コントラクト情報を取得中...\" \n    size={40}\n  />\n);\n\nexport const IPFSLoadingSpinner = () => (\n  <NFTLoadingSpinner \n    message=\"IPFSにアップロード中...\" \n    size={50}\n  />\n);\n\nexport const TransactionLoadingSpinner = () => (\n  <NFTLoadingSpinner \n    message=\"トランザクション処理中...\" \n    size={60}\n    fullScreen\n  />\n);\n\nexport default NFTLoadingSpinner;