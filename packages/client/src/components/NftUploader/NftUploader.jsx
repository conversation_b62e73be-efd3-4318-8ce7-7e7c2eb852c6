        '',\n        validation.alternativeImageUrl ? `🔧 代替URL: ${validation.alternativeImageUrl}` : '',\n        '',\n        '💡 ヒント:',\n        validation.isEtherscanCompatible ? \n          '・このNFTはEtherscanで正常に表示されます' :\n          '・IPFS伝播に時間がかかる場合があります\\n・数分後に再度確認してください',\n        '',\n        '詳細はコンソールを確認してください。'\n      ].filter(Boolean).join('\\n');\n      \n      alert(resultMessage);\n      \n    } catch (error) {\n      console.error('Etherscan検証エラー:', error);\n      alert(`検証中にエラーが発生しました: ${error.message}`);\n    } finally {\n      setUploading(false);\n      setLoadingStep('');\n      setLoadingProgress(0);\n    }\n  }, [mintedNftInfo]);\n  \n  // メタデータ簡易確認（従来版）\n  const handleQuickMetadataCheck = useCallback(async () => {\n    if (!mintedNftInfo) return;\n    \n    try {\n      const { ethereum } = window;\n      const provider = new ethers.BrowserProvider(ethereum);\n      const contract = new ethers.Contract(\n        mintedNftInfo.contractAddress,\n        Web3Mint.abi,\n        provider\n      );\n      \n      const tokenURI = await contract.tokenURI(mintedNftInfo.tokenId);\n      console.log(`\\n📄 Token ${mintedNftInfo.tokenId} Metadata URI:`);\n      console.log(tokenURI);\n      \n      alert(`メタデータURI:\\n${tokenURI}\\n\\n詳細はコンソールを確認してください。`);\n    } catch (error) {\n      console.error('メタデータ取得エラー:', error);\n      alert('メタデータの取得に失敗しました');\n    }\n  }, [mintedNftInfo]);\n\n  // 接続していない状態のUI（メモ化）\n  const renderNotConnectedContainer = useMemo(() => (\n    <button\n      onClick={connectWallet}\n      className=\"cta-button connect-wallet-button\"\n      disabled={isConnecting || isPending}\n    >\n      {(isConnecting || isPending) ? '接続中...' : 'Connect to Wallet'}\n    </button>\n  ), [connectWallet, isConnecting, isPending]);\n\n  return (\n    <div className=\"outerBox\">\n      {currentAccount === \"\" ? (\n        renderNotConnectedContainer\n      ) : (\n        <p>画像を選択してオリジナルNFTを作成しましょう！</p>\n      )}\n      \n      <div className=\"title\">\n        <h2>NFTアップローダー</h2>\n      </div>\n\n      {/* ネットワークエラーアラート */}\n      {networkError && (\n        <Alert \n          severity=\"warning\" \n          style={{ \n            margin: \"10px 0\",\n            whiteSpace: \"pre-line\"\n          }}\n          action={\n            <div style={{ display: 'flex', flexDirection: 'column', gap: '5px' }}>\n              <button\n                onClick={switchToSepolia}\n                style={{\n                  padding: \"5px 10px\",\n                  backgroundColor: \"#ff9800\",\n                  color: \"white\",\n                  border: \"none\",\n                  borderRadius: \"4px\",\n                  cursor: \"pointer\",\n                  fontSize: \"0.8em\"\n                }}\n              >\n                Sepoliaに切り替え\n              </button>\n            </div>\n          }\n        >\n          {networkError}\n        </Alert>\n      )}\n\n      {/* エラー・成功メッセージ */}\n      {walletError && (\n        <Alert severity=\"error\" style={{ margin: \"10px 0\" }}>\n          {walletError}\n        </Alert>\n      )}\n      {success && (\n        <Alert severity=\"success\" style={{ margin: \"10px 0\" }}>\n          {success}\n        </Alert>\n      )}\n\n      {/* NFT作成成功時の詳細情報 */}\n      {mintedNftInfo && (\n        <div style={{\n          marginTop: \"20px\",\n          padding: \"20px\",\n          backgroundColor: \"#e8f5e8\",\n          borderRadius: \"8px\",\n          border: \"1px solid #4caf50\"\n        }}>\n          <h3 style={{ margin: \"0 0 15px 0\", color: \"#2e7d32\" }}>🎉 NFT作成成功！</h3>\n          \n          <div style={{ marginBottom: \"15px\" }}>\n            <div><strong>📋 Contract Address:</strong></div>\n            <div style={{ \n              fontFamily: \"monospace\", \n              fontSize: \"0.9em\", \n              backgroundColor: \"#f5f5f5\", \n              padding: \"5px\", \n              borderRadius: \"4px\",\n              wordBreak: \"break-all\"\n            }}>\n              {mintedNftInfo.contractAddress}\n            </div>\n          </div>\n          \n          <div style={{ marginBottom: \"15px\" }}>\n            <div><strong>🏷️ Token ID:</strong> {mintedNftInfo.tokenId}</div>\n            <div><strong>📄 Transaction:</strong></div>\n            <div style={{ \n              fontFamily: \"monospace\", \n              fontSize: \"0.8em\", \n              backgroundColor: \"#f5f5f5\", \n              padding: \"5px\", \n              borderRadius: \"4px\",\n              wordBreak: \"break-all\"\n            }}>\n              {mintedNftInfo.txHash}\n            </div>\n          </div>\n          \n          <div style={{ marginTop: \"20px\" }}>\n            <h4 style={{ margin: \"0 0 10px 0\" }}>🔗 確認リンク</h4>\n            \n            <div style={{ marginBottom: \"10px\" }}>\n              <a \n                href={`https://${mintedNftInfo.networkName}.etherscan.io/tx/${mintedNftInfo.txHash}`}\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                style={{\n                  display: \"inline-block\",\n                  padding: \"8px 16px\",\n                  backgroundColor: \"#1976d2\",\n                  color: \"white\",\n                  textDecoration: \"none\",\n                  borderRadius: \"4px\",\n                  marginRight: \"10px\"\n                }}\n              >\n                🔍 Etherscanで確認\n              </a>\n              \n              <a \n                href=\"https://gemcase.vercel.app/\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                style={{\n                  display: \"inline-block\",\n                  padding: \"8px 16px\",\n                  backgroundColor: \"#9c27b0\",\n                  color: \"white\",\n                  textDecoration: \"none\",\n                  borderRadius: \"4px\"\n                }}\n              >\n                🖼️ Gemcaseで確認\n              </a>\n            </div>\n            \n            <div style={{ \n              fontSize: \"0.9em\", \n              color: \"#666\", \n              backgroundColor: \"#f9f9f9\", \n              padding: \"10px\", \n              borderRadius: \"4px\",\n              marginTop: \"10px\"\n            }}>\n              <div><strong>🎯 Etherscan表示について:</strong></div>\n              <div style={{ marginTop: '5px', lineHeight: '1.4' }}>\n                ・「Etherscan表示検証」ボタンで画像表示をテスト可能<br />\n                ・IPFS伝播には数分～数時間かかる場合があります<br />\n                ・画像が表示されない場合は時間をおいて再確認してください<br />\n                ・メインネットでは安定した表示が期待できます\n              </div>\n            </div>\n            \n            <div style={{ \n              fontSize: \"0.9em\", \n              color: \"#666\", \n              backgroundColor: \"#f9f9f9\", \n              padding: \"10px\", \n              borderRadius: \"4px\",\n              marginTop: \"5px\"\n            }}>\n              <div><strong>Gemcaseでの入力手順:</strong></div>\n              <div>1. 上記リンクでGemcaseを開く</div>\n              <div>2. Addressフィールドに Contract Address を入力</div>\n              <div>3. Token IDフィールドに {mintedNftInfo.tokenId} を入力</div>\n            </div>\n            \n            {/* Etherscan検証ボタン（メイン） */}\n            <button \n              onClick={handleEtherscanValidation}\n              disabled={uploading}\n              style={{\n                marginTop: \"10px\",\n                padding: \"8px 16px\",\n                backgroundColor: uploading ? \"#ccc\" : \"#ff9800\",\n                color: \"white\",\n                border: \"none\",\n                borderRadius: \"4px\",\n                cursor: uploading ? \"not-allowed\" : \"pointer\",\n                marginRight: \"10px\",\n                fontWeight: \"bold\"\n              }}\n            >\n              🎯 Etherscan表示検証\n            </button>\n            \n            {/* メタデータ簡易確認ボタン */}\n            <button \n              onClick={handleQuickMetadataCheck}\n              style={{\n                marginTop: \"10px\",\n                padding: \"5px 10px\",\n                backgroundColor: \"#2196f3\",\n                color: \"white\",\n                border: \"none\",\n                borderRadius: \"4px\",\n                cursor: \"pointer\",\n                marginRight: \"10px\"\n              }}\n            >\n              📄 メタデータURI確認\n            </button>\n            \n            {/* Etherscanリアルタイム監視ステータス */}\n            {tokenUriForValidation && (\n              <EtherscanStatus \n                tokenUri={tokenUriForValidation}\n                onStatusUpdate={(status) => {\n                  console.log('🔄 Etherscan表示ステータス更新:', status);\n                }}\n              />\n            )}\n          </div>\n          \n          <button \n            onClick={() => startTransition(() => {\n              setMintedNftInfo(null);\n              setTokenUriForValidation(null);\n            })}\n            style={{\n              marginTop: \"15px\",\n              padding: \"5px 10px\",\n              backgroundColor: \"#f5f5f5\",\n              border: \"1px solid #ccc\",\n              borderRadius: \"4px\",\n              cursor: \"pointer\"\n            }}\n          >\n            閉じる\n          </button>\n        </div>\n      )}\n\n      {/* ローディングアニメーション */}\n      {(uploading || isPending) && (\n        <div style={{\n          position: \"fixed\",\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          backgroundColor: \"rgba(0, 0, 0, 0.8)\",\n          display: \"flex\",\n          justifyContent: \"center\",\n          alignItems: \"center\",\n          zIndex: 9999\n        }}>\n          <div style={{\n            backgroundColor: \"white\",\n            padding: \"40px\",\n            borderRadius: \"12px\",\n            textAlign: \"center\",\n            maxWidth: \"400px\",\n            width: \"90%\",\n            boxShadow: \"0 8px 32px rgba(0, 0, 0, 0.3)\"\n          }}>\n            <div style={{ marginBottom: \"20px\" }}>\n              <CircularProgress \n                size={60} \n                variant=\"determinate\" \n                value={loadingProgress}\n                style={{ color: \"#1976d2\" }}\n              />\n            </div>\n            \n            <div style={{\n              fontSize: \"1.2em\",\n              fontWeight: \"bold\",\n              marginBottom: \"10px\",\n              color: \"#333\"\n            }}>\n              {isPending ? \"UI更新中...\" : \"NFT作成中...\"}\n            </div>\n            \n            <div style={{\n              fontSize: \"0.9em\",\n              color: \"#666\",\n              marginBottom: \"15px\",\n              minHeight: \"20px\"\n            }}>\n              {loadingStep}\n            </div>\n            \n            <div style={{\n              width: \"100%\",\n              height: \"8px\",\n              backgroundColor: \"#f0f0f0\",\n              borderRadius: \"4px\",\n              overflow: \"hidden\",\n              marginBottom: \"10px\"\n            }}>\n              <div style={{\n                width: `${loadingProgress}%`,\n                height: \"100%\",\n                backgroundColor: \"#1976d2\",\n                borderRadius: \"4px\",\n                transition: \"width 0.3s ease\"\n              }} />\n            </div>\n            \n            <div style={{\n              fontSize: \"0.8em\",\n              color: \"#999\"\n            }}>\n              {loadingProgress}% 完了\n            </div>\n            \n            <div style={{\n              marginTop: \"20px\",\n              fontSize: \"0.8em\",\n              color: \"#666\",\n              lineHeight: \"1.4\"\n            }}>\n              ⚠️ この処理には数分かかる場合があります。<br />\n              ブラウザを閉じないでお待ちください。\n            </div>\n          </div>\n        </div>\n      )}\n\n      <div className=\"nftUplodeBox\">\n        <div className=\"imageLogoAndText\">\n          <img src={ImageLogo} alt=\"imagelogo\" />\n          <p>ここにドラッグ＆ドロップしてね</p>\n        </div>\n        <input\n          className=\"nftUploadInput\"\n          name=\"imageURL\"\n          type=\"file\"\n          accept=\".jpg,.jpeg,.png,.gif,.svg\"\n          onChange={handleFileSelect}\n          disabled={uploading || isPending}\n        />\n      </div>\n      \n      <p>または</p>\n      \n      <Button variant=\"contained\" component=\"label\" disabled={uploading || isPending}>\n        ファイルを選択\n        <input\n          className=\"nftUploadInput\"\n          type=\"file\"\n          accept=\".jpg,.jpeg,.png,.gif,.svg\"\n          onChange={handleFileSelect}\n          hidden\n          disabled={uploading || isPending}\n        />\n      </Button>\n\n      {/* 選択されたファイル情報 */}\n      {selectedFile && (\n        <div style={{ margin: \"20px 0\", textAlign: \"center\" }}>\n          <p>選択されたファイル: <strong>{selectedFile.name}</strong></p>\n          <p>サイズ: {Math.round(selectedFile.size / 1024)} KB</p>\n          \n          <Button \n            variant=\"contained\" \n            color=\"primary\"\n            onClick={imageToNFT}\n            disabled={uploading || !currentAccount || !!networkError || isPending}\n            style={{ margin: \"10px\" }}\n          >\n            {(uploading || isPending) ? (\n              \"NFT作成中...\"\n            ) : (\n              \"NFTを作成\"\n            )}\n          </Button>\n        </div>\n      )}\n\n      {/* 現在のアカウント表示 */}\n      {currentAccount && (\n        <div style={{ marginTop: \"20px\", fontSize: \"0.9em\", color: \"#666\" }}>\n          接続中のアカウント: {currentAccount.slice(0, 6)}...{currentAccount.slice(-4)}\n        </div>\n      )}\n\n      {/* IPFSサービス状態表示 */}\n      <div style={{\n        marginTop: \"20px\",\n        padding: \"15px\",\n        backgroundColor: isUsingRealIPFS ? \"#e8f5e8\" : \"#fff3cd\",\n        border: `1px solid ${isUsingRealIPFS ? \"#4caf50\" : \"#ffeaa7\"}`,\n        borderRadius: \"8px\",\n        fontSize: \"0.9em\"\n      }}>\n        <div style={{ fontWeight: \"bold\", marginBottom: \"10px\", color: isUsingRealIPFS ? \"#2e7d32\" : \"#856404\" }}>\n          {isUsingRealIPFS ? \"🌍 IPFSサービス状態\" : \"📝 IPFSサービス設定\"}\n        </div>\n        <div style={{ color: isUsingRealIPFS ? \"#2e7d32\" : \"#856404\", lineHeight: \"1.5\" }}>\n          {isUsingRealIPFS ? (\n            <>\n              <div><strong>現在の状態:</strong></div>\n              <div>✅ <strong>Storacha (w3up)</strong>を使用中（実際IPFS）</div>\n              <div>✅ 画像が正常に表示されます</div>\n              <div>✅ データは永続的に保存されます</div>\n            </>\n          ) : (\n            <>\n              <div><strong>現在の状態:</strong></div>\n              <div>🧪 <strong>モックIPFS</strong>を使用中（テスト用）</div>\n              <div>⚠️ 画像は表示されません</div>\n              <div>⚠️ テストネットでのみ使用可能</div>\n              \n              <div style={{ marginTop: \"10px\" }}><strong>実際IPFSを使用するには:</strong></div>\n              <div>1. <a href=\"https://console.storacha.network/\" target=\"_blank\" rel=\"noopener noreferrer\" style={{ color: \"#856404\" }}>console.storacha.network</a> でアカウント作成</div>\n              <div>2. メール認証を完了</div>\n              <div>3. .envファイルに REACT_APP_W3UP_EMAIL=<EMAIL> を追加</div>\n              <div>4. アプリを再起動</div>\n            </>\n          )}\n        </div>\n      </div>\n\n      {/* コントラクト情報表示 */}\n      {contractInfo && (\n        <div style={{ \n          marginTop: \"20px\", \n          padding: \"15px\", \n          backgroundColor: \"#f5f5f5\", \n          borderRadius: \"8px\",\n          fontSize: \"0.9em\"\n        }}>\n          <div style={{ fontWeight: \"bold\", marginBottom: \"10px\" }}>コントラクト情報</div>\n          <div>ミント機能: {contractInfo.mintingEnabled ? \"✅ 有効\" : \"❌ 無効\"}</div>\n          <div>ミント価格: {contractInfo.mintPrice} ETH</div>\n          <div>発行数: {contractInfo.currentSupply} / {contractInfo.maxSupply}</div>\n          {contractInfo.isMaxReached && (\n            <div style={{ color: \"#ff6b6b\", marginTop: \"5px\" }}>\n              ⚠️ 最大発行数に達しています\n            </div>\n          )}\n          {!contractInfo.mintingEnabled && (\n            <div style={{ color: \"#ff6b6b\", marginTop: \"5px\" }}>\n              ⚠️ ミント機能が無効です\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default NftUploader;