import { ethers } from 'ethers';
import { useCallback, useState, useTransition } from 'react';
import Web3Mint from '../../../utils/Web3Mint.json';
import { getIPFSUploader } from '../../../utils/mockIPFS';

/**
 * NFTミント処理用カスタムフック
 * 責務：
 * - NFTミント処理の管理
 * - 進捗状態の管理
 * - エラーハンドリング
 */
const useNftMinting = () => {
  const [isPending, startTransition] = useTransition();

  const [uploading, setUploading] = useState(false);
  const [loadingStep, setLoadingStep] = useState('');
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [mintedNftInfo, setMintedNftInfo] = useState(null);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // コントラクトアドレス
  const CONTRACT_ADDRESS = process.env.REACT_APP_CONTRACT_ADDRESS ||
    '******************************************';
  const NETWORK_NAME = process.env.REACT_APP_NETWORK_NAME || 'sepolia';

  // 進捗更新
  const updateProgress = useCallback((step, progress) => {
    setLoadingStep(step);
    setLoadingProgress(progress);
  }, []);

  // NFTミント処理
  const mintNFT = useCallback(async (file, currentAccount) => {
    if (!file || !currentAccount) {
      throw new Error('ファイルまたはアカウントが指定されていません');
    }

    try {
      setUploading(true);
      setError('');
      setSuccess('');
      updateProgress('ミント処理を開始しています...', 0);

      // 1. IPFSアップロード
      updateProgress('IPFSに画像をアップロード中...', 20);
      const ipfsUploader = getIPFSUploader();

      const fileName = file.name.replace(/\.[^/.]+$/, ""); // 拡張子を除去
      const metadataURI = await ipfsUploader.uploadNFTData(
        file,
        fileName,
        `${fileName} - Created with NFT Maker`
      );

      updateProgress('スマートコントラクトを呼び出し中...', 60);

      // 2. スマートコントラクト呼び出し
      const { ethereum } = window;
      if (!ethereum) {
        throw new Error('MetaMaskが見つかりません');
      }

      const provider = new ethers.BrowserProvider(ethereum);
      const signer = await provider.getSigner();
      const contract = new ethers.Contract(CONTRACT_ADDRESS, Web3Mint.abi, signer);

      // コントラクト状態を確認
      const mintingEnabled = await contract.mintingEnabled();
      const mintPrice = await contract.mintPrice();
      const currentSupply = await contract.totalSupply();
      const maxSupply = await contract.MAX_SUPPLY();

      console.log('📊 コントラクト状態確認:');
      console.log('  ミント機能:', mintingEnabled ? '有効' : '無効');
      console.log('  ミント価格:', ethers.formatEther(mintPrice), 'ETH');
      console.log('  現在の発行数:', currentSupply.toString());
      console.log('  最大発行数:', maxSupply.toString());
      console.log('  送信ETH:', ethers.formatEther(mintPrice), 'ETH');

      // ミント前の検証
      if (!mintingEnabled) {
        throw new Error('ミント機能が無効になっています');
      }

      if (currentSupply >= maxSupply) {
        throw new Error('最大発行数に達しています');
      }

      updateProgress('トランザクションを送信中...', 80);

      // NFTをミント
      // mintIpfsNFT(name, description, ipfsHash) - toアドレスは不要
      console.log('🚀 ミント実行中...');
      console.log('  名前:', fileName);
      console.log('  説明:', `${fileName} - Created with NFT Maker`);
      console.log('  メタデータURI:', metadataURI);
      console.log('  送信ETH:', ethers.formatEther(mintPrice));

      let transaction;
      try {
        transaction = await contract.mintIpfsNFT(
          fileName,
          `${fileName} - Created with NFT Maker`,
          metadataURI,
          {
            value: mintPrice,
            gasLimit: 500000
          }
        );

        console.log('✅ トランザクション送信成功:', transaction.hash);
      } catch (txError) {
        console.error('❌ トランザクション送信エラー:', txError);

        // より詳細なエラー情報を取得
        if (txError.reason) {
          throw new Error(`コントラクトエラー: ${txError.reason}`);
        } else if (txError.code === 'CALL_EXCEPTION') {
          throw new Error('コントラクト実行エラー: 条件を満たしていない可能性があります');
        } else {
          throw txError;
        }
      }

      updateProgress('トランザクション確認中...', 90);

      const receipt = await transaction.wait();

      if (receipt.status === 0) {
        console.error('❌ トランザクション失敗:', receipt);
        throw new Error('トランザクションが失敗しました。コントラクトの条件を確認してください。');
      }

      console.log('✅ トランザクション完了:', receipt);

      // トークンIDを取得
      const tokenId = await contract.getCurrentTokenId();

      updateProgress('NFT作成完了！', 100);

      const nftInfo = {
        contractAddress: CONTRACT_ADDRESS,
        tokenId: tokenId.toString(),
        txHash: receipt.hash,
        networkName: NETWORK_NAME,
        metadataURI,
        fileName: file.name
      };

      startTransition(() => {
        setMintedNftInfo(nftInfo);
        setSuccess('NFTが正常に作成されました！');
      });

      console.log('🎉 NFT作成成功:', nftInfo);
      return nftInfo;

    } catch (error) {
      console.error('❌ NFTミントエラー:', error);

      let errorMessage = 'NFTの作成に失敗しました';

      if (error.code === 4001) {
        errorMessage = 'ユーザーによってトランザクションが拒否されました';
      } else if (error.code === 'INSUFFICIENT_FUNDS') {
        errorMessage = 'ETHが不足しています';
      } else if (error.message.includes('MintingDisabled')) {
        errorMessage = 'ミント機能が無効になっています';
      } else if (error.message.includes('MaxSupplyExceeded')) {
        errorMessage = '最大発行数に達しています';
      } else if (error.message.includes('InsufficientPayment')) {
        errorMessage = '支払い金額が不足しています';
      } else if (error.message) {
        errorMessage = error.message;
      }

      setError(errorMessage);
      throw error;
    } finally {
      setUploading(false);
      setLoadingStep('');
      setLoadingProgress(0);
    }
  }, [CONTRACT_ADDRESS, NETWORK_NAME, updateProgress]);

  // ミント情報をクリア
  const clearMintedInfo = useCallback(() => {
    startTransition(() => {
      setMintedNftInfo(null);
      setSuccess('');
    });
  }, []);

  // エラーをクリア
  const clearError = useCallback(() => {
    setError('');
  }, []);

  return {
    // 状態
    uploading: uploading || isPending,
    loadingStep,
    loadingProgress,
    mintedNftInfo,
    error,
    success,
    isPending,

    // アクション
    mintNFT,
    clearMintedInfo,
    clearError,

    // ユーティリティ
    isMinting: uploading,
    hasError: !!error,
    hasSuccess: !!success,
    hasMintedNft: !!mintedNftInfo
  };
};

export default useNftMinting;
