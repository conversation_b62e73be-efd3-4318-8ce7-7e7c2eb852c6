/**
 * Etherscan画像表示問題の詳細調査ツール
 */

import { ethers } from 'ethers';
import Web3Mint from './Web3Mint.json';

/**
 * 包括的なEtherscan表示調査
 */
export const investigateEtherscanDisplay = async (contractAddress, tokenId) => {
  console.log('🔍 Etherscan表示問題の詳細調査開始');
  console.log('==========================================');
  console.log(`📋 Contract: ${contractAddress}`);
  console.log(`🏷️ Token ID: ${tokenId}`);

  const investigation = {
    timestamp: new Date().toISOString(),
    contractAddress,
    tokenId,
    steps: [],
    issues: [],
    recommendations: []
  };

  try {
    // Step 1: コントラクト基本情報
    investigation.steps.push(await investigateContract(contractAddress, tokenId));

    // Step 2: メタデータ詳細調査
    const tokenURI = investigation.steps[0].tokenURI;
    if (tokenURI) {
      investigation.steps.push(await investigateMetadata(tokenURI));

      // Step 3: 画像アクセシビリティ調査
      const metadata = investigation.steps[1].metadata;
      if (metadata?.image) {
        investigation.steps.push(await investigateImageAccess(metadata.image));

        // Step 4: IPFS伝播状況調査
        investigation.steps.push(await investigateIPFSPropagation(metadata.image));

        // Step 5: Etherscan互換性調査
        investigation.steps.push(await investigateEtherscanCompatibility(metadata));
      }
    }

    // Step 6: 総合分析
    investigation.analysis = analyzeResults(investigation.steps);

  } catch (error) {
    investigation.error = error.message;
    console.error('❌ 調査中にエラー:', error);
  }

  console.log('\n📊 調査完了');
  console.log('==========================================');

  return investigation;
};

/**
 * Step 1: コントラクト基本情報調査
 */
const investigateContract = async (contractAddress, tokenId) => {
  const step = {
    step: 1,
    name: 'コントラクト基本情報',
    success: false,
    details: {},
    timestamp: new Date().toISOString()
  };

  try {
    console.log('\n🔍 Step 1: コントラクト基本情報調査...');

    const { ethereum } = window;
    const provider = new ethers.BrowserProvider(ethereum);
    const contract = new ethers.Contract(contractAddress, Web3Mint.abi, provider);

    // 基本情報取得
    const [name, symbol, tokenURI, owner, totalSupply] = await Promise.all([
      contract.name(),
      contract.symbol(),
      contract.tokenURI(tokenId),
      contract.ownerOf(tokenId),
      contract.totalSupply()
    ]);

    step.success = true;
    step.details = {
      contractName: name,
      symbol,
      tokenURI,
      owner,
      totalSupply: totalSupply.toString()
    };
    step.tokenURI = tokenURI;

    console.log(`✅ コントラクト名: ${name}`);
    console.log(`✅ シンボル: ${symbol}`);
    console.log(`✅ Token URI: ${tokenURI}`);
    console.log(`✅ 所有者: ${owner}`);
    console.log(`✅ 総発行数: ${totalSupply}`);

  } catch (error) {
    step.error = error.message;
    console.log(`❌ コントラクト情報取得失敗: ${error.message}`);
  }

  return step;
};

/**
 * Step 2: メタデータ詳細調査
 */
const investigateMetadata = async (tokenURI) => {
  const step = {
    step: 2,
    name: 'メタデータ詳細調査',
    success: false,
    details: {},
    timestamp: new Date().toISOString()
  };

  try {
    console.log('\n🔍 Step 2: メタデータ詳細調査...');
    console.log(`📄 Token URI: ${tokenURI}`);

    // IPFS URIをHTTPS URLに変換
    const httpsUrl = tokenURI.startsWith('ipfs://')
      ? tokenURI.replace('ipfs://', 'https://ipfs.io/ipfs/')
      : tokenURI;

    console.log(`🔗 HTTPS URL: ${httpsUrl}`);

    // メタデータ取得
    const response = await fetch(httpsUrl);
    step.details.httpStatus = response.status;
    step.details.responseHeaders = Object.fromEntries(response.headers.entries());

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const metadata = await response.json();
    step.success = true;
    step.metadata = metadata;
    step.details.metadata = metadata;

    console.log('✅ メタデータ取得成功');
    console.log('📄 メタデータ内容:', JSON.stringify(metadata, null, 2));

    // メタデータ構造分析
    step.details.analysis = {
      hasName: !!metadata.name,
      hasDescription: !!metadata.description,
      hasImage: !!metadata.image,
      hasAttributes: Array.isArray(metadata.attributes),
      imageUrl: metadata.image,
      imageProtocol: metadata.image?.split('://')[0],
      attributeCount: metadata.attributes?.length || 0
    };

  } catch (error) {
    step.error = error.message;
    console.log(`❌ メタデータ取得失敗: ${error.message}`);
  }

  return step;
};

/**
 * Step 3: 画像アクセシビリティ調査
 */
const investigateImageAccess = async (imageUrl) => {
  const step = {
    step: 3,
    name: '画像アクセシビリティ調査',
    success: false,
    details: {},
    timestamp: new Date().toISOString()
  };

  try {
    console.log('\n🔍 Step 3: 画像アクセシビリティ調査...');
    console.log(`🖼️ 画像URL: ${imageUrl}`);

    const startTime = Date.now();

    // HEADリクエストで画像情報取得
    const response = await fetch(imageUrl, {
      method: 'HEAD',
      mode: 'cors'
    });

    const responseTime = Date.now() - startTime;

    step.details = {
      url: imageUrl,
      httpStatus: response.status,
      responseTime,
      contentType: response.headers.get('content-type'),
      contentLength: response.headers.get('content-length'),
      cacheControl: response.headers.get('cache-control'),
      corsHeaders: {
        accessControlAllowOrigin: response.headers.get('access-control-allow-origin'),
        accessControlAllowMethods: response.headers.get('access-control-allow-methods')
      },
      isImageType: response.headers.get('content-type')?.startsWith('image/') || false
    };

    step.success = response.ok;

    if (response.ok) {
      console.log(`✅ 画像アクセス成功 (${responseTime}ms)`);
      console.log(`📄 Content-Type: ${response.headers.get('content-type')}`);
      console.log(`📊 Size: ${response.headers.get('content-length')} bytes`);
    } else {
      console.log(`❌ 画像アクセス失敗: HTTP ${response.status}`);
    }

  } catch (error) {
    step.error = error.message;
    step.details.errorType = error.name;
    console.log(`❌ 画像アクセスエラー: ${error.message}`);
  }

  return step;
};

/**
 * Step 4: IPFS伝播状況調査
 */
const investigateIPFSPropagation = async (imageUrl) => {
  const step = {
    step: 4,
    name: 'IPFS伝播状況調査',
    success: false,
    details: { gateways: [] },
    timestamp: new Date().toISOString()
  };

  try {
    console.log('\n🔍 Step 4: IPFS伝播状況調査...');

    // CIDを抽出
    const cidMatch = imageUrl.match(/\/ipfs\/([^/?]+)/);
    if (!cidMatch) {
      throw new Error('IPFS CIDが見つかりません');
    }

    const cid = cidMatch[1];
    console.log(`🏷️ IPFS CID: ${cid}`);

    // 複数のゲートウェイでテスト
    const gateways = {
      'ipfs.io': `https://ipfs.io/ipfs/${cid}`,
      'w3s.link': `https://w3s.link/ipfs/${cid}`,
      'dweb.link': `https://dweb.link/ipfs/${cid}`,
      'gateway.pinata.cloud': `https://gateway.pinata.cloud/ipfs/${cid}`,
      'cf-ipfs.com': `https://cf-ipfs.com/ipfs/${cid}`,
      'cloudflare-ipfs.com': `https://cloudflare-ipfs.com/ipfs/${cid}`
    };

    let successCount = 0;

    for (const [name, url] of Object.entries(gateways)) {
      const gatewayResult = {
        name,
        url,
        success: false,
        responseTime: null,
        error: null
      };

      try {
        console.log(`🧪 Testing ${name}...`);
        const startTime = Date.now();

        const response = await fetch(url, {
          method: 'HEAD',
          signal: AbortSignal.timeout(8000)
        });

        gatewayResult.responseTime = Date.now() - startTime;
        gatewayResult.success = response.ok;
        gatewayResult.httpStatus = response.status;

        if (response.ok) {
          successCount++;
          console.log(`✅ ${name}: OK (${gatewayResult.responseTime}ms)`);
        } else {
          console.log(`❌ ${name}: HTTP ${response.status}`);
        }

      } catch (error) {
        gatewayResult.error = error.message;
        console.log(`❌ ${name}: ${error.message}`);
      }

      step.details.gateways.push(gatewayResult);
    }

    step.details.successCount = successCount;
    step.details.totalGateways = Object.keys(gateways).length;
    step.details.propagationRate = (successCount / Object.keys(gateways).length) * 100;
    step.success = successCount > 0;

    console.log(`📊 伝播状況: ${successCount}/${Object.keys(gateways).length} (${step.details.propagationRate.toFixed(1)}%)`);

  } catch (error) {
    step.error = error.message;
    console.log(`❌ IPFS伝播調査エラー: ${error.message}`);
  }

  return step;
};

/**
 * Step 5: Etherscan互換性調査
 */
const investigateEtherscanCompatibility = async (metadata) => {
  const step = {
    step: 5,
    name: 'Etherscan互換性調査',
    success: false,
    details: {},
    timestamp: new Date().toISOString()
  };

  try {
    console.log('\n🔍 Step 5: Etherscan互換性調査...');

    const compatibility = {
      hasName: !!metadata.name,
      hasDescription: !!metadata.description,
      hasImage: !!metadata.image,
      imageIsHttps: metadata.image?.startsWith('https://'),
      imageIsIPFS: metadata.image?.includes('/ipfs/'),
      hasValidAttributes: Array.isArray(metadata.attributes) && metadata.attributes.length > 0,
      metadataStructure: 'valid'
    };

    // Etherscan要件チェック
    const requirements = [
      { name: '名前', check: compatibility.hasName, required: true },
      { name: '説明', check: compatibility.hasDescription, required: true },
      { name: '画像URL', check: compatibility.hasImage, required: true },
      { name: 'HTTPS画像', check: compatibility.imageIsHttps, required: false },
      { name: '属性', check: compatibility.hasValidAttributes, required: false }
    ];

    step.details.compatibility = compatibility;
    step.details.requirements = requirements;

    const passedRequired = requirements.filter(r => r.required && r.check).length;
    const totalRequired = requirements.filter(r => r.required).length;

    step.success = passedRequired === totalRequired;
    step.details.score = (passedRequired / totalRequired) * 100;

    console.log('📋 Etherscan互換性チェック:');
    requirements.forEach(req => {
      const status = req.check ? '✅' : '❌';
      const priority = req.required ? '(必須)' : '(推奨)';
      console.log(`  ${status} ${req.name} ${priority}`);
    });

    console.log(`📊 互換性スコア: ${step.details.score}%`);

  } catch (error) {
    step.error = error.message;
    console.log(`❌ Etherscan互換性調査エラー: ${error.message}`);
  }

  return step;
};

/**
 * 総合分析
 */
const analyzeResults = (steps) => {
  const analysis = {
    overallStatus: 'unknown',
    issues: [],
    recommendations: [],
    etherscanDisplayProbability: 0
  };

  // 各ステップの結果を分析
  const contractStep = steps.find(s => s.step === 1);
  const metadataStep = steps.find(s => s.step === 2);
  const imageStep = steps.find(s => s.step === 3);
  const ipfsStep = steps.find(s => s.step === 4);
  const compatibilityStep = steps.find(s => s.step === 5);

  // 問題の特定
  if (!contractStep?.success) {
    analysis.issues.push('コントラクト情報の取得に失敗');
  }

  if (!metadataStep?.success) {
    analysis.issues.push('メタデータの取得に失敗');
    analysis.recommendations.push('IPFS伝播を待つか、異なるゲートウェイを試してください');
  }

  if (!imageStep?.success) {
    analysis.issues.push('画像へのアクセスに失敗');
    analysis.recommendations.push('画像のIPFS伝播を待つか、代替ゲートウェイを使用してください');
  }

  if (ipfsStep?.details?.propagationRate < 50) {
    analysis.issues.push(`IPFS伝播率が低い (${ipfsStep.details.propagationRate.toFixed(1)}%)`);
    analysis.recommendations.push('IPFS伝播の完了を待ってください（数時間かかる場合があります）');
  }

  if (!compatibilityStep?.success) {
    analysis.issues.push('Etherscan互換性に問題');
    analysis.recommendations.push('メタデータ構造を確認してください');
  }

  // Etherscan表示確率の計算
  let probability = 0;
  if (contractStep?.success) probability += 20;
  if (metadataStep?.success) probability += 30;
  if (imageStep?.success) probability += 25;
  if (ipfsStep?.details?.propagationRate > 50) probability += 15;
  if (compatibilityStep?.success) probability += 10;

  analysis.etherscanDisplayProbability = probability;

  // 総合ステータス
  if (probability >= 80) {
    analysis.overallStatus = 'excellent';
    analysis.recommendations.push('✅ Etherscanで正常に表示される可能性が高いです');
  } else if (probability >= 60) {
    analysis.overallStatus = 'good';
    analysis.recommendations.push('⚠️ 時間をおいて再確認してください');
  } else if (probability >= 40) {
    analysis.overallStatus = 'poor';
    analysis.recommendations.push('❌ 表示に問題があります。IPFS伝播を待つか、設定を確認してください');
  } else {
    analysis.overallStatus = 'failed';
    analysis.recommendations.push('🚨 重大な問題があります。メタデータとIPFS設定を確認してください');
  }

  return analysis;
};

// ブラウザコンソール用の関数
if (typeof window !== 'undefined') {
  window.investigateEtherscanDisplay = investigateEtherscanDisplay;

  // 簡単なヘルプ関数も追加
  window.etherscanHelp = () => {
    console.log('🔍 Etherscan表示調査ツール');
    console.log('==========================================');
    console.log('');
    console.log('📋 使用方法:');
    console.log('investigateEtherscanDisplay("CONTRACT_ADDRESS", "TOKEN_ID")');
    console.log('');
    console.log('🔍 例:');
    console.log('investigateEtherscanDisplay("******************************************", "13")');
    console.log('');
    console.log('💡 このツールは以下を調査します:');
    console.log('  • コントラクト基本情報');
    console.log('  • メタデータアクセシビリティ');
    console.log('  • 画像アクセシビリティ');
    console.log('  • IPFS伝播状況');
    console.log('  • Etherscan互換性');
    console.log('');
    console.log('==========================================');
  };

  console.log('🔍 Etherscan調査ツールが利用可能です');
  console.log('💡 使い方: etherscanHelp()');
}

export default investigateEtherscanDisplay;
