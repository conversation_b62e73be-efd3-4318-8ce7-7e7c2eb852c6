/**
 * 開発・テスト用のモックIPFSサービス
 * w3up (Web3.Storage v2) 統合版 - Etherscan対応版
 *
 * 【Etherscan対応改善】
 * - IPFS URIの代わりにHTTPS Gateway URLを使用
 * - 複数のIPFSゲートウェイに対応
 * - CORS対応の改善
 */

// w3upの動的インポート（ES6対応）
let w3upClient = null;

const initializeW3up = async () => {
  if (process.env.REACT_APP_W3UP_EMAIL && !w3upClient) {
    try {
      console.log('🚀 w3up: 初期化を開始します...');
      console.log(`Email: ${process.env.REACT_APP_W3UP_EMAIL}`);

      const Client = await import('@web3-storage/w3up-client');
      const client = await Client.create();

      console.log('💻 w3up: クライアント作成完了');
      console.log('Accounts:', Object.keys(client.accounts()).length);

      // アカウントがない場合は初期設定
      if (!Object.keys(client.accounts()).length) {
        console.log('📧 w3up: Email認証を行います...');
        console.log('メールで送られたリンクをクリックして認証してください。');

        try {
          // メール認証を開始（非同期）
          const account = await client.login(process.env.REACT_APP_W3UP_EMAIL);
          console.log('✅ w3up: アカウントログイン完了');

          // Spaceを作成
          const space = await client.createSpace('nft-storage');
          await space.save();
          await account.provision(space.did());

          console.log('✅ w3up: 初期設定完了');
        } catch (loginError) {
          console.log('⚠️ w3up: ログインエラー - モックサービスを使用:', loginError.message);
          return null;
        }
      } else {
        console.log('✅ w3up: 既存アカウントを使用');
      }

      w3upClient = client;
      console.log('🌍 w3up client initialized successfully');
      return client;
    } catch (error) {
      console.log('🧪 w3up initialization failed, using mock service:', error.message);
      console.error('w3up error details:', error);
      return null;
    }
  }

  if (w3upClient) {
    console.log('🔄 w3up: 既存クライアントを使用');
  }

  return w3upClient;
};

/**
 * 🔧 Etherscan対応のIPFSゲートウェイ設定
 */
const ETHERSCAN_COMPATIBLE_GATEWAYS = {
  // 優先度順（上から順に試行）
  ipfs_io: 'https://ipfs.io/ipfs/',                    // 標準・信頼性高・Etherscan対応
  w3s_link: 'https://w3s.link/ipfs/',                 // Web3.Storage専用・高速
  dweb: 'https://dweb.link/ipfs/',                    // 分散Web対応・安定
  gateway_pinata: 'https://gateway.pinata.cloud/ipfs/',  // Pinata・商用対応
  cf_ipfs: 'https://cf-ipfs.com/ipfs/'               // Cloudflare・高速
};

/**
 * 🌐 CIDからEtherscan対応のHTTPS URLを生成
 * @param {string} cid - IPFS CID
 * @param {string} gateway - 使用するゲートウェイ（デフォルト: cloudflare）
 * @returns {string} HTTPS URL
 */
const generateEtherscanCompatibleUrl = (cid, gateway = 'ipfs_io') => {
  const gatewayUrl = ETHERSCAN_COMPATIBLE_GATEWAYS[gateway] || ETHERSCAN_COMPATIBLE_GATEWAYS.ipfs_io;
  const httpsUrl = `${gatewayUrl}${cid}`;

  console.log(`🔗 CID: ${cid}`);
  console.log(`🌐 Gateway: ${gateway}`);
  console.log(`🔗 HTTPS URL: ${httpsUrl}`);

  return httpsUrl;
};

/**
 * モックIPFSアップロード関数（Etherscan対応版）
 */
export const mockUploadToIPFS = async (file) => {
  // 実際のアップロード時間をシミュレート
  await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

  // ファイル名とタイムスタンプを使用して一意のCIDを生成
  const timestamp = Date.now();
  const fileHash = btoa(file.name + file.size + timestamp).replace(/[^a-zA-Z0-9]/g, '');
  const mockCID = `Qm${fileHash.slice(0, 44)}`;

  console.log(`🧪 Mock IPFS upload: ${file.name} -> ${mockCID}`);

  // Etherscan対応のHTTPS URLを生成
  const httpsUrl = generateEtherscanCompatibleUrl(mockCID);

  return {
    cid: mockCID,
    httpsUrl: httpsUrl,
    ipfsUri: `ipfs://${mockCID}`
  };
};

/**
 * 実際のw3up関数（Etherscan対応版）
 */
const realUploadToIPFS = async (file) => {
  try {
    console.log(`🌍 Uploading ${file.name} to w3up (Etherscan compatible)...`);
    console.log(`📊 File details: ${file.size} bytes, ${file.type}`);

    const client = await initializeW3up();
    if (!client) {
      throw new Error('w3up client not available');
    }

    console.log('📤 Starting file upload...');
    const cid = await client.uploadFile(file);
    const cidString = cid.toString();

    console.log(`✅ File uploaded successfully!`);
    console.log(`📸 Image CID: ${cidString}`);

    // 複数のHTTPS URLを生成
    const urls = {
      ipfs_io: generateEtherscanCompatibleUrl(cidString, 'ipfs_io'),
      w3s_link: generateEtherscanCompatibleUrl(cidString, 'w3s_link'),
      dweb: generateEtherscanCompatibleUrl(cidString, 'dweb'),
      gateway_pinata: generateEtherscanCompatibleUrl(cidString, 'gateway_pinata'),
      cf_ipfs: generateEtherscanCompatibleUrl(cidString, 'cf_ipfs')
    };

    console.log('🌐 Generated HTTPS URLs:');
    console.log('  IPFS.io:', urls.ipfs_io);
    console.log('  W3S.link:', urls.w3s_link);
    console.log('  Dweb.link:', urls.dweb);
    console.log('  Pinata:', urls.gateway_pinata);
    console.log('  CF-IPFS:', urls.cf_ipfs);

    // 最適なURLの選択（IPFS.ioを優先）
    const primaryUrl = urls.ipfs_io;

    // URLのアクセシビリティテスト
    console.log('🔍 Testing HTTPS URL accessibility...');
    try {
      const response = await fetch(primaryUrl, {
        method: 'HEAD',
        timeout: 5000,
        signal: AbortSignal.timeout(5000)
      });
      if (response.ok) {
        console.log('✅ Primary URL immediately accessible');
      } else {
        console.log(`⚠️ Primary URL not yet accessible: ${response.status}`);
        console.log('ℹ️ IPFS propagation in progress...');

        // 他のゲートウェイを試行
        console.log('🔄 Trying alternative gateways...');
        const accessibleUrl = await findAccessibleGateway(cidString);
        console.log('🎯 Alternative URL found:', accessibleUrl);
      }
    } catch (testError) {
      console.log('⚠️ URL accessibility test failed:', testError.message);
      console.log('ℹ️ This is normal - IPFS propagation takes time');

      // フォールバック: 他のゲートウェイを試行
      try {
        console.log('🔄 Trying alternative gateways...');
        const accessibleUrl = await findAccessibleGateway(cidString);
        console.log('🎯 Fallback URL found:', accessibleUrl);
      } catch (fallbackError) {
        console.log('⚠️ All gateways unavailable, using default URL');
      }
    }

    return {
      cid: cidString,
      httpsUrl: primaryUrl,
      ipfsUri: `ipfs://${cidString}`,
      alternativeUrls: urls
    };
  } catch (error) {
    console.error('❌ w3up upload error:', error);
    throw new Error(`IPFS upload failed: ${error.message}`);
  }
};

const realUploadMetadata = async (metadata) => {
  try {
    console.log('🌍 Uploading metadata to w3up (Etherscan compatible)...');
    console.log('📄 Metadata content:', JSON.stringify(metadata, null, 2));

    const client = await initializeW3up();
    if (!client) {
      throw new Error('w3up client not available');
    }

    const metadataBlob = new Blob([JSON.stringify(metadata, null, 2)], {
      type: 'application/json'
    });

    const metadataFile = new File([metadataBlob], 'metadata.json', {
      type: 'application/json'
    });

    console.log(`📊 Metadata file size: ${metadataFile.size} bytes`);
    console.log('📤 Starting metadata upload...');

    const cid = await client.uploadFile(metadataFile);
    const cidString = cid.toString();

    console.log(`✅ Metadata uploaded successfully!`);
    console.log(`📄 Metadata CID: ${cidString}`);

    // Etherscan対応のHTTPS URLを生成（IPFS.ioを使用）
    const httpsUrl = generateEtherscanCompatibleUrl(cidString, 'ipfs_io');

    return {
      cid: cidString,
      httpsUrl: httpsUrl,
      ipfsUri: `ipfs://${cidString}`
    };
  } catch (error) {
    console.error('❌ w3up metadata upload error:', error);
    throw new Error(`Metadata upload failed: ${error.message}`);
  }
};

/**
 * 🎨 Etherscan対応のNFTデータアップロード（完全版）
 */
const realUploadNFTData = async (imageFile, name, description) => {
  try {
    console.log('🚀 Starting Etherscan-compatible NFT data upload...');
    console.log(`📁 Image: ${imageFile.name} (${Math.round(imageFile.size / 1024)} KB)`);

    // 1. 画像をアップロード
    console.log('🚀 Step 1: 画像ファイルをIPFSにアップロード中...');
    const imageResult = await realUploadToIPFS(imageFile);

    console.log('🔍 画像アップロード結果:');
    console.log('  📸 画像CID:', imageResult.cid);
    console.log('  📸 画像IPFS URI:', imageResult.ipfsUri);
    console.log('  📸 画像HTTPS URL:', imageResult.httpsUrl);

    // 🔧 Etherscan対応: imageResult.httpsUrlを直接使用
    const imageURI = imageResult.httpsUrl;

    console.log('🔍 メタデータ用画像URI:');
    console.log('  📸 使用する画像URL:', imageURI);
    console.log('  📸 HTTPS形式:', imageURI.startsWith('https://'));
    console.log('  📸 画像CIDを含む:', imageURI.includes(imageResult.cid));

    // 画像URLの検証
    if (!imageURI.startsWith('https://ipfs.io/ipfs/')) {
      console.error('❌ 警告: 画像URLが期待される形式ではありません');
      console.error('  Expected: https://ipfs.io/ipfs/...');
      console.error('  Actual:', imageURI);
    }

    // 画像CIDの検証
    if (!imageURI.includes(imageResult.cid)) {
      console.error('🚨 致命的エラー: 画像URLに正しいCIDが含まれていません!');
      console.error('  画像CID:', imageResult.cid);
      console.error('  画像URL:', imageURI);
      throw new Error('画像URLが正しくありません');
    }

    // 2. Etherscan/OpenSea互換メタデータ作成
    const metadata = {
      name,
      description,
      image: imageURI,  // 強制的にHTTPS URL を使用
      external_url: imageURI,  // 外部リンクも同じHTTPS URLを使用
      attributes: [
        {
          trait_type: "Upload Date",
          value: new Date().toISOString().split('T')[0]
        },
        {
          trait_type: "File Type",
          value: imageFile.type
        },
        {
          trait_type: "File Size (KB)",
          value: Math.round(imageFile.size / 1024)
        },
        {
          trait_type: "Upload Timestamp",
          value: Date.now()
        },
        {
          trait_type: "Storage Provider",
          value: "w3up (Web3.Storage v2)"
        },
        {
          trait_type: "IPFS CID",
          value: imageResult.cid
        },
        {
          trait_type: "Gateway",
          value: "IPFS.io"
        },
        {
          trait_type: "Etherscan Compatible",
          value: "Yes"
        }
      ]
    };

    console.log('📄 Generated Etherscan-compatible metadata:', metadata);

    // メタデータの最終検証
    console.log('🔍 Final metadata verification:');
    console.log('  Name:', metadata.name);
    console.log('  Description:', metadata.description);
    console.log('  Image URL:', metadata.image);
    console.log('  Image is HTTPS:', metadata.image.startsWith('https://'));
    console.log('  External URL:', metadata.external_url);

    // HTTPS URLの強制確認
    if (!metadata.image.startsWith('https://')) {
      console.error('🚨 致命的エラー: メタデータの画像URLがHTTPS形式ではありません!');
      console.error('  Expected: https://...');
      console.error('  Actual:', metadata.image);
      throw new Error('画像URLがHTTPS形式ではありません。Etherscanで表示されません。');
    }

    console.log('✅ メタデータ検証完了: Etherscan互換性OK');

    // 3. メタデータをアップロード
    console.log('🚀 Step 3: メタデータをIPFSにアップロード中...');
    const metadataResult = await realUploadMetadata(metadata);

    console.log('🔍 メタデータアップロード結果:');
    console.log('  📄 メタデータCID:', metadataResult.cid);
    console.log('  📄 メタデータIPFS URI:', metadataResult.ipfsUri);
    console.log('  📄 メタデータHTTPS URL:', metadataResult.httpsUrl);

    console.log('🎉 Etherscan-compatible NFT data upload completed!');
    console.log('🔗 Results Summary:');
    console.log(`  📸 Image HTTPS URL: ${imageResult.httpsUrl}`);
    console.log(`  📄 Metadata HTTPS URL: ${metadataResult.httpsUrl}`);
    console.log(`  🏷️ Image CID: ${imageResult.cid}`);
    console.log(`  🏷️ Metadata CID: ${metadataResult.cid}`);

    // CIDの比較検証
    console.log('🔍 CID検証:');
    console.log(`  📸 画像CID: ${imageResult.cid}`);
    console.log(`  📄 メタデータCID: ${metadataResult.cid}`);
    console.log(`  🔄 CIDが異なる: ${imageResult.cid !== metadataResult.cid}`);
    console.log(`  📸 メタデータ内画像URL: ${metadata.image}`);
    console.log(`  ✅ 画像URLに画像CIDが含まれる: ${metadata.image.includes(imageResult.cid)}`);

    if (imageResult.cid === metadataResult.cid) {
      console.error('🚨 致命的エラー: 画像CIDとメタデータCIDが同じです!');
      console.error('これは画像とメタデータが混同されていることを示します');
    }

    if (!metadata.image.includes(imageResult.cid)) {
      console.error('🚨 致命的エラー: メタデータ内の画像URLに正しい画像CIDが含まれていません!');
      console.error('  期待される画像CID:', imageResult.cid);
      console.error('  実際の画像URL:', metadata.image);
    }

    console.log('  ✅ Etherscan Display: Compatible');
    console.log('  ✅ OpenSea Display: Compatible');

    // コントラクトで使用するためのIPFS URIを返す（互換性のため）
    // ただし、実際の画像URLはHTTPS形式で保存される
    return metadataResult.ipfsUri;  // メタデータのIPFS URI

  } catch (error) {
    console.error('❌ Etherscan-compatible NFT data upload error:', error);
    throw error;
  }
};

/**
 * モックメタデータアップロード（Etherscan対応版）
 */
export const mockUploadMetadata = async (metadata) => {
  await new Promise(resolve => setTimeout(resolve, 500));

  const metadataStr = JSON.stringify(metadata);
  const metadataHash = btoa(metadataStr + Date.now()).replace(/[^a-zA-Z0-9]/g, '');
  const mockCID = `Qm${metadataHash.slice(0, 44)}`;

  console.log(`🧪 Mock metadata upload:`, metadata);
  console.log(`🧪 Mock metadata CID: ${mockCID}`);

  // Etherscan対応のHTTPS URLを生成
  const httpsUrl = generateEtherscanCompatibleUrl(mockCID);

  return {
    cid: mockCID,
    httpsUrl: httpsUrl,
    ipfsUri: `ipfs://${mockCID}`
  };
};

/**
 * モックNFTデータアップロード（Etherscan対応版）
 */
export const mockUploadNFTData = async (imageFile, name, description) => {
  try {
    console.log("🧪 Mock IPFS: Starting Etherscan-compatible upload simulation...");

    // 1. 画像アップロードをシミュレート
    const imageResult = await mockUploadToIPFS(imageFile);

    // 🔧 Etherscan対応: HTTPS URLを使用
    const imageURI = imageResult.httpsUrl;

    // 2. Etherscan互換メタデータ作成
    const metadata = {
      name,
      description,
      image: imageURI,  // HTTPS URL を使用
      external_url: imageResult.httpsUrl,
      attributes: [
        {
          trait_type: "Upload Date",
          value: new Date().toISOString().split('T')[0]
        },
        {
          trait_type: "File Type",
          value: imageFile.type
        },
        {
          trait_type: "File Size (KB)",
          value: Math.round(imageFile.size / 1024)
        },
        {
          trait_type: "Mock Upload",
          value: "true"
        },
        {
          trait_type: "Etherscan Compatible",
          value: "Yes"
        }
      ]
    };

    // 3. メタデータアップロードをシミュレート
    const metadataResult = await mockUploadMetadata(metadata);

    console.log("🧪 Mock IPFS: Etherscan-compatible upload complete!");
    console.log("📸 Image CID:", imageResult.cid);
    console.log("📸 Image HTTPS URL:", imageResult.httpsUrl);
    console.log("📄 Metadata CID:", metadataResult.cid);
    console.log("📄 Metadata HTTPS URL:", metadataResult.httpsUrl);
    console.log("✅ Etherscan Compatible: Yes");

    return metadataResult.ipfsUri;  // メタデータのIPFS URI
  } catch (error) {
    console.error("❌ Mock Etherscan-compatible upload error:", error);
    throw error;
  }
};

/**
 * 開発環境検出用ユーティリティ
 */
export const isDevelopment = () => {
  return process.env.NODE_ENV === 'development';
};

/**
 * w3up利用可能性チェック
 */
export const isWeb3StorageAvailable = () => {
  return !!process.env.REACT_APP_W3UP_EMAIL;
};

/**
 * 環境に応じてIPFSサービスを選択（Etherscan対応版）
 */
export const getIPFSUploader = () => {
  console.log('🔍 getIPFSUploader: Etherscan対応サービス選択開始');
  console.log('Environment variables:');
  console.log('- REACT_APP_W3UP_EMAIL:', !!process.env.REACT_APP_W3UP_EMAIL);
  console.log('- NODE_ENV:', process.env.NODE_ENV);

  // w3upが利用可能かチェック
  if (isWeb3StorageAvailable()) {
    console.log("🌍 w3up email found - Real Etherscan-compatible IPFS will be used");
    console.log('ℹ️ Etherscan対応w3upサービスを返します');
    return {
      uploadToIPFS: realUploadToIPFS,
      uploadMetadata: realUploadMetadata,
      uploadNFTData: realUploadNFTData
    };
  } else {
    console.log("ℹ️ w3up email not found. To use real IPFS:");
    console.log("   1. Sign up at: https://console.storacha.network/");
    console.log("   2. Add REACT_APP_W3UP_EMAIL=<EMAIL> to .env file");
    console.log("   3. Follow email verification process");
    console.log("🧪 Using Etherscan-compatible mock IPFS service");
    console.log('ℹ️ Etherscan対応モックサービスを返します');

    return {
      uploadToIPFS: mockUploadToIPFS,
      uploadMetadata: mockUploadMetadata,
      uploadNFTData: mockUploadNFTData
    };
  }
};

/**
 * 🔧 既存のIPFS URIをHTTPS URLに変換するユーティリティ
 * @param {string} ipfsUri - ipfs://QmXXXX 形式のURI
 * @param {string} gateway - 使用するゲートウェイ
 * @returns {string} HTTPS URL
 */
export const convertIpfsToHttps = (ipfsUri, gateway = 'ipfs_io') => {
  if (!ipfsUri.startsWith('ipfs://')) {
    return ipfsUri;  // 既にHTTPS URLの場合はそのまま返す
  }

  const cid = ipfsUri.replace('ipfs://', '');
  return generateEtherscanCompatibleUrl(cid, gateway);
};

/**
 * 🎯 Etherscan NFT表示用の最適化されたメタデータ検証
 * @param {string} metadataUri - メタデータのURI
 * @returns {Promise<Object>} 検証結果
 */
export const validateEtherscanNFTDisplay = async (metadataUri) => {
  console.log('🎯 Validating NFT metadata for Etherscan display:', metadataUri);

  try {
    // URIをHTTPS URLに変換
    const httpsUrl = convertIpfsToHttps(metadataUri);
    console.log('🔗 HTTPS URL:', httpsUrl);

    // メタデータを取得
    const response = await fetch(httpsUrl);
    if (!response.ok) {
      throw new Error(`Failed to fetch metadata: ${response.status}`);
    }

    const metadata = await response.json();
    console.log('📄 Retrieved metadata:', metadata);

    // Etherscan表示要件チェック
    const validation = {
      hasName: !!metadata.name,
      hasDescription: !!metadata.description,
      hasImage: !!metadata.image,
      imageIsHttps: metadata.image?.startsWith('https://'),
      hasAttributes: Array.isArray(metadata.attributes),
      metadataAccessible: true,
      imageUrl: metadata.image,
      requirements: {
        name: metadata.name || 'Missing',
        description: metadata.description || 'Missing',
        image: metadata.image || 'Missing',
        attributesCount: metadata.attributes?.length || 0
      }
    };

    // 画像アクセシビリティテスト
    if (metadata.image) {
      try {
        const imageResponse = await fetch(metadata.image, { method: 'HEAD', timeout: 5000 });
        validation.imageAccessible = imageResponse.ok;
        validation.imageContentType = imageResponse.headers.get('content-type');
      } catch (error) {
        validation.imageAccessible = false;
        validation.imageError = error.message;
      }
    }

    // 総合判定
    validation.isEtherscanCompatible =
      validation.hasName &&
      validation.hasDescription &&
      validation.hasImage &&
      validation.imageIsHttps &&
      validation.metadataAccessible;

    console.log('✅ Validation complete:', validation);
    return validation;

  } catch (error) {
    console.error('❌ Validation failed:', error);
    return {
      isEtherscanCompatible: false,
      error: error.message,
      metadataAccessible: false
    };
  }
};

/**
 * 🔄 複数ゲートウェイで順次アクセス試行
 * @param {string} cid - IPFS CID
 * @returns {Promise<string>} アクセス可能な最初のURL
 */
export const findAccessibleGateway = async (cid) => {
  const gateways = Object.keys(ETHERSCAN_COMPATIBLE_GATEWAYS);

  console.log(`🔍 Testing ${gateways.length} gateways for CID: ${cid}`);

  for (const gateway of gateways) {
    const url = generateEtherscanCompatibleUrl(cid, gateway);
    console.log(`⏳ Testing ${gateway}: ${url}`);

    try {
      const response = await fetch(url, {
        method: 'HEAD',
        timeout: 5000,
        signal: AbortSignal.timeout(5000)
      });

      if (response.ok) {
        console.log(`✅ ${gateway} is accessible`);
        return url;
      } else {
        console.log(`❌ ${gateway} returned ${response.status}`);
      }
    } catch (error) {
      console.log(`❌ ${gateway} failed: ${error.message}`);
    }
  }

  // すべて失敗した場合はデフォルトを返す
  console.log('⚠️ All gateways failed, returning default IPFS.io URL');
  return generateEtherscanCompatibleUrl(cid, 'ipfs_io');
};

/**
 * 🧪 Etherscan互換性テスト関数
 * @param {string} imageUrl - テストする画像URL
 * @returns {Promise<Object>} テスト結果
 */
export const testEtherscanCompatibility = async (imageUrl) => {
  console.log('🧪 Testing Etherscan compatibility for:', imageUrl);

  const results = {
    url: imageUrl,
    isHttps: imageUrl.startsWith('https://'),
    isAccessible: false,
    hasCors: false,
    responseTime: null,
    contentType: null,
    errors: []
  };

  try {
    const startTime = Date.now();

    const response = await fetch(imageUrl, {
      method: 'HEAD',
      mode: 'cors'
    });

    results.responseTime = Date.now() - startTime;
    results.isAccessible = response.ok;
    results.contentType = response.headers.get('content-type');
    results.hasCors = !!response.headers.get('access-control-allow-origin');

    if (response.ok) {
      console.log('✅ URL is accessible');
      console.log(`⏱️ Response time: ${results.responseTime}ms`);
      console.log(`📄 Content-Type: ${results.contentType}`);
      console.log(`🌐 CORS: ${results.hasCors ? 'Enabled' : 'Disabled'}`);
    } else {
      results.errors.push(`HTTP ${response.status}: ${response.statusText}`);
    }

  } catch (error) {
    results.errors.push(error.message);
    console.log('❌ Accessibility test failed:', error.message);
  }

  // 互換性判定
  const isEtherscanCompatible = results.isHttps && results.isAccessible;

  console.log(`🎯 Etherscan Compatible: ${isEtherscanCompatible ? 'Yes' : 'No'}`);

  return {
    ...results,
    isEtherscanCompatible
  };
};
