/**\n * Etherscan表示診断ツール\n * IPFS URLの問題を詳細に分析するためのデバッグユーティリティ\n */\n\nimport { validateEtherscanNFTDisplay, findAccessibleGateway, convertIpfsToHttps } from '../utils/mockIPFS';\n\n/**\n * 🔍 総合診断レポート生成\n * @param {string} contractAddress - コントラクトアドレス\n * @param {number} tokenId - トークンID\n * @returns {Promise<Object>} 診断結果\n */\nexport const generateEtherscanDiagnosticReport = async (contractAddress, tokenId) => {\n  console.log('\\n🏥 Etherscan表示診断開始');\n  console.log('================================');\n  console.log(`📋 Contract: ${contractAddress}`);\n  console.log(`🏷️ Token ID: ${tokenId}`);\n  \n  const report = {\n    timestamp: new Date().toISOString(),\n    contractAddress,\n    tokenId,\n    steps: [],\n    summary: {\n      overallStatus: 'unknown',\n      issues: [],\n      recommendations: []\n    }\n  };\n  \n  try {\n    // Step 1: コントラクトからtokenURIを取得\n    report.steps.push(await diagnoseTokenURI(contractAddress, tokenId));\n    \n    // Step 2: メタデータの取得と解析\n    const tokenURI = report.steps[0].result;\n    if (tokenURI) {\n      report.steps.push(await diagnoseMetadata(tokenURI));\n      \n      // Step 3: 画像URLの詳細解析\n      const metadata = report.steps[1].result;\n      if (metadata?.image) {\n        report.steps.push(await diagnoseImageAccess(metadata.image));\n        \n        // Step 4: 代替ゲートウェイテスト\n        if (!report.steps[2].success) {\n          report.steps.push(await diagnoseAlternativeGateways(metadata.image));\n        }\n      }\n    }\n    \n    // Step 5: 総合判定\n    report.summary = generateSummary(report.steps);\n    \n  } catch (error) {\n    report.steps.push({\n      step: 'error',\n      description: 'Critical error during diagnosis',\n      success: false,\n      error: error.message,\n      timestamp: new Date().toISOString()\n    });\n  }\n  \n  console.log('\\n📊 診断レポート完了');\n  console.log('================================');\n  console.log(JSON.stringify(report, null, 2));\n  \n  return report;\n};\n\n/**\n * Step 1: tokenURI取得診断\n */\nconst diagnoseTokenURI = async (contractAddress, tokenId) => {\n  const step = {\n    step: 1,\n    description: 'コントラクトからtokenURIを取得',\n    success: false,\n    details: {},\n    timestamp: new Date().toISOString()\n  };\n  \n  try {\n    console.log('\\n🔍 Step 1: tokenURI取得中...');\n    \n    const { ethers } = window;\n    const provider = new ethers.BrowserProvider(window.ethereum);\n    \n    // ERC721標準のtokenURI関数を呼び出し\n    const contract = new ethers.Contract(\n      contractAddress,\n      ['function tokenURI(uint256 tokenId) view returns (string)'],\n      provider\n    );\n    \n    const tokenURI = await contract.tokenURI(tokenId);\n    \n    step.success = !!tokenURI;\n    step.result = tokenURI;\n    step.details = {\n      uri: tokenURI,\n      isHttps: tokenURI?.startsWith('https://'),\n      isIpfs: tokenURI?.startsWith('ipfs://'),\n      length: tokenURI?.length || 0\n    };\n    \n    console.log(`✅ tokenURI取得成功: ${tokenURI}`);\n    \n  } catch (error) {\n    step.error = error.message;\n    console.log(`❌ tokenURI取得失敗: ${error.message}`);\n  }\n  \n  return step;\n};\n\n/**\n * Step 2: メタデータ解析診断\n */\nconst diagnoseMetadata = async (tokenURI) => {\n  const step = {\n    step: 2,\n    description: 'メタデータの取得と解析',\n    success: false,\n    details: {},\n    timestamp: new Date().toISOString()\n  };\n  \n  try {\n    console.log('\\n🔍 Step 2: メタデータ解析中...');\n    \n    // IPFS URIをHTTPS URLに変換\n    const httpsUrl = convertIpfsToHttps(tokenURI);\n    \n    step.details.originalUri = tokenURI;\n    step.details.httpsUrl = httpsUrl;\n    \n    console.log(`🔗 変換後URL: ${httpsUrl}`);\n    \n    // メタデータを取得\n    const response = await fetch(httpsUrl, {\n      method: 'GET',\n      headers: {\n        'Accept': 'application/json',\n        'Cache-Control': 'no-cache'\n      }\n    });\n    \n    step.details.httpStatus = response.status;\n    step.details.responseHeaders = Object.fromEntries(response.headers.entries());\n    \n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n    }\n    \n    const metadata = await response.json();\n    \n    step.success = true;\n    step.result = metadata;\n    step.details.metadata = {\n      hasName: !!metadata.name,\n      hasDescription: !!metadata.description,\n      hasImage: !!metadata.image,\n      hasAttributes: Array.isArray(metadata.attributes),\n      imageUrl: metadata.image,\n      attributeCount: metadata.attributes?.length || 0\n    };\n    \n    console.log(`✅ メタデータ取得成功`);\n    console.log(`📄 Name: ${metadata.name}`);\n    console.log(`🖼️ Image: ${metadata.image}`);\n    \n  } catch (error) {\n    step.error = error.message;\n    console.log(`❌ メタデータ取得失敗: ${error.message}`);\n  }\n  \n  return step;\n};\n\n/**\n * Step 3: 画像アクセス診断\n */\nconst diagnoseImageAccess = async (imageUrl) => {\n  const step = {\n    step: 3,\n    description: '画像URLアクセシビリティテスト',\n    success: false,\n    details: {},\n    timestamp: new Date().toISOString()\n  };\n  \n  try {\n    console.log('\\n🔍 Step 3: 画像アクセステスト中...');\n    console.log(`🖼️ Image URL: ${imageUrl}`);\n    \n    const startTime = Date.now();\n    \n    // HEADリクエストで画像の存在確認\n    const response = await fetch(imageUrl, {\n      method: 'HEAD',\n      mode: 'cors',\n      cache: 'no-cache'\n    });\n    \n    const responseTime = Date.now() - startTime;\n    \n    step.details = {\n      url: imageUrl,\n      httpStatus: response.status,\n      responseTime: responseTime,\n      contentType: response.headers.get('content-type'),\n      contentLength: response.headers.get('content-length'),\n      corsHeaders: {\n        accessControlAllowOrigin: response.headers.get('access-control-allow-origin'),\n        accessControlAllowMethods: response.headers.get('access-control-allow-methods')\n      },\n      isImageType: response.headers.get('content-type')?.startsWith('image/') || false\n    };\n    \n    step.success = response.ok;\n    \n    if (response.ok) {\n      console.log(`✅ 画像アクセス成功 (${responseTime}ms)`);\n      console.log(`📄 Content-Type: ${response.headers.get('content-type')}`);\n      console.log(`📊 Size: ${response.headers.get('content-length')} bytes`);\n    } else {\n      console.log(`❌ 画像アクセス失敗: HTTP ${response.status}`);\n    }\n    \n  } catch (error) {\n    step.error = error.message;\n    step.details.errorType = error.name;\n    console.log(`❌ 画像アクセスエラー: ${error.message}`);\n  }\n  \n  return step;\n};\n\n/**\n * Step 4: 代替ゲートウェイ診断\n */\nconst diagnoseAlternativeGateways = async (imageUrl) => {\n  const step = {\n    step: 4,\n    description: '代替IPFSゲートウェイテスト',\n    success: false,\n    details: { gateways: [] },\n    timestamp: new Date().toISOString()\n  };\n  \n  try {\n    console.log('\\n🔍 Step 4: 代替ゲートウェイテスト中...');\n    \n    // CIDを抽出\n    const cidMatch = imageUrl.match(/\\/ipfs\\/([^/?]+)/);\n    if (!cidMatch) {\n      throw new Error('IPFS CIDが見つかりません');\n    }\n    \n    const cid = cidMatch[1];\n    console.log(`🏷️ IPFS CID: ${cid}`);\n    \n    // 各ゲートウェイをテスト\n    const gateways = {\n      'ipfs.io': `https://ipfs.io/ipfs/${cid}`,\n      'w3s.link': `https://w3s.link/ipfs/${cid}`,\n      'dweb.link': `https://dweb.link/ipfs/${cid}`,\n      'gateway.pinata.cloud': `https://gateway.pinata.cloud/ipfs/${cid}`,\n      'cf-ipfs.com': `https://cf-ipfs.com/ipfs/${cid}`\n    };\n    \n    for (const [name, url] of Object.entries(gateways)) {\n      const gatewayResult = {\n        name,\n        url,\n        success: false,\n        responseTime: null,\n        error: null\n      };\n      \n      try {\n        console.log(`🧪 Testing ${name}...`);\n        const startTime = Date.now();\n        \n        const response = await fetch(url, {\n          method: 'HEAD',\n          timeout: 5000,\n          signal: AbortSignal.timeout(5000)\n        });\n        \n        gatewayResult.responseTime = Date.now() - startTime;\n        gatewayResult.success = response.ok;\n        gatewayResult.httpStatus = response.status;\n        \n        if (response.ok) {\n          console.log(`✅ ${name}: OK (${gatewayResult.responseTime}ms)`);\n          if (!step.success) {\n            step.success = true;\n            step.result = url;\n          }\n        } else {\n          console.log(`❌ ${name}: HTTP ${response.status}`);\n        }\n        \n      } catch (error) {\n        gatewayResult.error = error.message;\n        console.log(`❌ ${name}: ${error.message}`);\n      }\n      \n      step.details.gateways.push(gatewayResult);\n    }\n    \n  } catch (error) {\n    step.error = error.message;\n    console.log(`❌ 代替ゲートウェイテストエラー: ${error.message}`);\n  }\n  \n  return step;\n};\n\n/**\n * 総合判定の生成\n */\nconst generateSummary = (steps) => {\n  const summary = {\n    overallStatus: 'unknown',\n    issues: [],\n    recommendations: [],\n    successfulSteps: 0,\n    totalSteps: steps.length\n  };\n  \n  // 成功したステップをカウント\n  summary.successfulSteps = steps.filter(step => step.success).length;\n  \n  // 各ステップの結果を分析\n  steps.forEach(step => {\n    if (!step.success) {\n      summary.issues.push({\n        step: step.step,\n        description: step.description,\n        error: step.error\n      });\n    }\n  });\n  \n  // 総合ステータス判定\n  if (summary.successfulSteps === summary.totalSteps) {\n    summary.overallStatus = 'excellent';\n    summary.recommendations.push('✅ NFTはEtherscanで正常に表示されます');\n  } else if (summary.successfulSteps >= 2) {\n    summary.overallStatus = 'good';\n    summary.recommendations.push('⚠️ 一部の問題がありますが、表示される可能性があります');\n  } else if (summary.successfulSteps >= 1) {\n    summary.overallStatus = 'poor';\n    summary.recommendations.push('❌ 表示に問題があります。IPFS伝播を待つか、代替手段を検討してください');\n  } else {\n    summary.overallStatus = 'failed';\n    summary.recommendations.push('🚨 重大な問題があります。コントラクトやメタデータを確認してください');\n  }\n  \n  // 具体的な推奨事項\n  if (summary.issues.some(issue => issue.step === 2)) {\n    summary.recommendations.push('💡 メタデータが取得できません。IPFSの伝播を待つか、異なるゲートウェイを試してください');\n  }\n  \n  if (summary.issues.some(issue => issue.step === 3)) {\n    summary.recommendations.push('💡 画像ファイルにアクセスできません。時間をおいて再確認するか、代替URLを使用してください');\n  }\n  \n  return summary;\n};\n\n/**\n * 🖥️ ブラウザコンソール用の簡易診断関数\n */\nwindow.debugEtherscanNFT = async (contractAddress, tokenId) => {\n  const report = await generateEtherscanDiagnosticReport(contractAddress, tokenId);\n  \n  console.log('\\n🎯 診断結果サマリー');\n  console.log('================================');\n  console.log(`📊 総合ステータス: ${report.summary.overallStatus}`);\n  console.log(`✅ 成功したステップ: ${report.summary.successfulSteps}/${report.summary.totalSteps}`);\n  \n  console.log('\\n🔧 推奨事項:');\n  report.summary.recommendations.forEach(rec => {\n    console.log(`  ${rec}`);\n  });\n  \n  if (report.summary.issues.length > 0) {\n    console.log('\\n⚠️ 発見された問題:');\n    report.summary.issues.forEach(issue => {\n      console.log(`  Step ${issue.step}: ${issue.description} - ${issue.error}`);\n    });\n  }\n  \n  return report;\n};\n\nexport default {\n  generateEtherscanDiagnosticReport,\n  debugEtherscanNFT: window.debugEtherscanNFT\n};\n