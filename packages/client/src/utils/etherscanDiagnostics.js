/**
 * Etherscan表示診断ツール
 * IPFS URLの問題を詳細に分析するためのデバッグユーティリティ
 */

/**
 * 🔍 総合診断レポート生成
 * @param {string} contractAddress - コントラクトアドレス
 * @param {number} tokenId - トークンID
 * @returns {Promise<Object>} 診断結果
 */
export const generateEtherscanDiagnosticReport = async (contractAddress, tokenId) => {
  console.log('\n🏥 Etherscan表示診断開始');
  console.log('================================');
  console.log(`📋 Contract: ${contractAddress}`);
  console.log(`🏷️ Token ID: ${tokenId}`);

  const report = {
    timestamp: new Date().toISOString(),
    contractAddress,
    tokenId,
    steps: [],
    summary: {
      overallStatus: 'unknown',
      issues: [],
      recommendations: []
    }
  };

  try {
    // Step 1: コントラクトからtokenURIを取得
    report.steps.push(await diagnoseTokenURI(contractAddress, tokenId));

    // Step 2: メタデータの取得と解析
    const tokenURI = report.steps[0].result;
    if (tokenURI) {
      report.steps.push(await diagnoseMetadata(tokenURI));

      // Step 3: 画像URLの詳細解析
      const metadata = report.steps[1].result;
      if (metadata?.image) {
        report.steps.push(await diagnoseImageAccess(metadata.image));

        // Step 4: 代替ゲートウェイテスト
        if (!report.steps[2].success) {
          report.steps.push(await diagnoseAlternativeGateways(metadata.image));
        }
      }
    }

    // Step 5: 総合判定
    report.summary = generateSummary(report.steps);

  } catch (error) {
    report.steps.push({
      step: 'error',
      description: 'Critical error during diagnosis',
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }

  console.log('\n📊 診断レポート完了');
  console.log('================================');
  console.log(JSON.stringify(report, null, 2));

  return report;
};

/**
 * Step 1: tokenURI取得診断
 */
const diagnoseTokenURI = async (contractAddress, tokenId) => {
  const step = {
    step: 1,
    description: 'コントラクトからtokenURIを取得',
    success: false,
    details: {},
    timestamp: new Date().toISOString()
  };

  try {
    console.log('\n🔍 Step 1: tokenURI取得中...');

    const { ethers } = window;
    const provider = new ethers.BrowserProvider(window.ethereum);

    // ERC721標準のtokenURI関数を呼び出し
    const contract = new ethers.Contract(
      contractAddress,
      ['function tokenURI(uint256 tokenId) view returns (string)'],
      provider
    );

    const tokenURI = await contract.tokenURI(tokenId);

    step.success = !!tokenURI;
    step.result = tokenURI;
    step.details = {
      uri: tokenURI,
      isHttps: tokenURI?.startsWith('https://'),
      isIpfs: tokenURI?.startsWith('ipfs://'),
      length: tokenURI?.length || 0
    };

    console.log(`✅ tokenURI取得成功: ${tokenURI}`);

  } catch (error) {
    step.error = error.message;
    console.log(`❌ tokenURI取得失敗: ${error.message}`);
  }

  return step;
};

/**
 * Step 2: メタデータ解析診断
 */
const diagnoseMetadata = async (tokenURI) => {
  const step = {
    step: 2,
    description: 'メタデータの取得と解析',
    success: false,
    details: {},
    timestamp: new Date().toISOString()
  };

  try {
    console.log('\n🔍 Step 2: メタデータ解析中...');

    // IPFS URIをHTTPS URLに変換（簡易版）
    let httpsUrl = tokenURI;
    if (tokenURI.startsWith('ipfs://')) {
      httpsUrl = tokenURI.replace('ipfs://', 'https://ipfs.io/ipfs/');
    }

    step.details.originalUri = tokenURI;
    step.details.httpsUrl = httpsUrl;

    console.log(`🔗 変換後URL: ${httpsUrl}`);

    // メタデータを取得
    const response = await fetch(httpsUrl, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Cache-Control': 'no-cache'
      }
    });

    step.details.httpStatus = response.status;

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const metadata = await response.json();

    step.success = true;
    step.result = metadata;
    step.details.metadata = {
      hasName: !!metadata.name,
      hasDescription: !!metadata.description,
      hasImage: !!metadata.image,
      hasAttributes: Array.isArray(metadata.attributes),
      imageUrl: metadata.image,
      attributeCount: metadata.attributes?.length || 0
    };

    console.log(`✅ メタデータ取得成功`);
    console.log(`📄 Name: ${metadata.name}`);
    console.log(`🖼️ Image: ${metadata.image}`);

  } catch (error) {
    step.error = error.message;
    console.log(`❌ メタデータ取得失敗: ${error.message}`);
  }

  return step;
};

/**
 * Step 3: 画像アクセス診断
 */
const diagnoseImageAccess = async (imageUrl) => {
  const step = {
    step: 3,
    description: '画像URLアクセシビリティテスト',
    success: false,
    details: {},
    timestamp: new Date().toISOString()
  };

  try {
    console.log('\n🔍 Step 3: 画像アクセステスト中...');
    console.log(`🖼️ Image URL: ${imageUrl}`);

    const startTime = Date.now();

    // HEADリクエストで画像の存在確認
    const response = await fetch(imageUrl, {
      method: 'HEAD',
      mode: 'cors',
      cache: 'no-cache'
    });

    const responseTime = Date.now() - startTime;

    step.details = {
      url: imageUrl,
      httpStatus: response.status,
      responseTime: responseTime,
      contentType: response.headers.get('content-type'),
      contentLength: response.headers.get('content-length'),
      isImageType: response.headers.get('content-type')?.startsWith('image/') || false
    };

    step.success = response.ok;

    if (response.ok) {
      console.log(`✅ 画像アクセス成功 (${responseTime}ms)`);
      console.log(`📄 Content-Type: ${response.headers.get('content-type')}`);
      console.log(`📊 Size: ${response.headers.get('content-length')} bytes`);
    } else {
      console.log(`❌ 画像アクセス失敗: HTTP ${response.status}`);
    }

  } catch (error) {
    step.error = error.message;
    step.details.errorType = error.name;
    console.log(`❌ 画像アクセスエラー: ${error.message}`);
  }

  return step;
};

/**
 * Step 4: 代替ゲートウェイ診断
 */
const diagnoseAlternativeGateways = async (imageUrl) => {
  const step = {
    step: 4,
    description: '代替IPFSゲートウェイテスト',
    success: false,
    details: { gateways: [] },
    timestamp: new Date().toISOString()
  };

  try {
    console.log('\n🔍 Step 4: 代替ゲートウェイテスト中...');

    // CIDを抽出
    const cidMatch = imageUrl.match(/\/ipfs\/([^/?]+)/);
    if (!cidMatch) {
      throw new Error('IPFS CIDが見つかりません');
    }

    const cid = cidMatch[1];
    console.log(`🏷️ IPFS CID: ${cid}`);

    // 各ゲートウェイをテスト
    const gateways = {
      'ipfs.io': `https://ipfs.io/ipfs/${cid}`,
      'w3s.link': `https://w3s.link/ipfs/${cid}`,
      'dweb.link': `https://dweb.link/ipfs/${cid}`,
      'gateway.pinata.cloud': `https://gateway.pinata.cloud/ipfs/${cid}`,
      'cf-ipfs.com': `https://cf-ipfs.com/ipfs/${cid}`
    };

    for (const [name, url] of Object.entries(gateways)) {
      const gatewayResult = {
        name,
        url,
        success: false,
        responseTime: null,
        error: null
      };

      try {
        console.log(`🧪 Testing ${name}...`);
        const startTime = Date.now();

        const response = await fetch(url, {
          method: 'HEAD',
          signal: AbortSignal.timeout(5000)
        });

        gatewayResult.responseTime = Date.now() - startTime;
        gatewayResult.success = response.ok;
        gatewayResult.httpStatus = response.status;

        if (response.ok) {
          console.log(`✅ ${name}: OK (${gatewayResult.responseTime}ms)`);
          if (!step.success) {
            step.success = true;
            step.result = url;
          }
        } else {
          console.log(`❌ ${name}: HTTP ${response.status}`);
        }

      } catch (error) {
        gatewayResult.error = error.message;
        console.log(`❌ ${name}: ${error.message}`);
      }

      step.details.gateways.push(gatewayResult);
    }

  } catch (error) {
    step.error = error.message;
    console.log(`❌ 代替ゲートウェイテストエラー: ${error.message}`);
  }

  return step;
};

/**
 * 総合判定の生成
 */
const generateSummary = (steps) => {
  const summary = {
    overallStatus: 'unknown',
    issues: [],
    recommendations: [],
    successfulSteps: 0,
    totalSteps: steps.length
  };

  // 成功したステップをカウント
  summary.successfulSteps = steps.filter(step => step.success).length;

  // 各ステップの結果を分析
  steps.forEach(step => {
    if (!step.success) {
      summary.issues.push({
        step: step.step,
        description: step.description,
        error: step.error
      });
    }
  });

  // 総合ステータス判定
  if (summary.successfulSteps === summary.totalSteps) {
    summary.overallStatus = 'excellent';
    summary.recommendations.push('✅ NFTはEtherscanで正常に表示されます');
  } else if (summary.successfulSteps >= 2) {
    summary.overallStatus = 'good';
    summary.recommendations.push('⚠️ 一部の問題がありますが、表示される可能性があります');
  } else if (summary.successfulSteps >= 1) {
    summary.overallStatus = 'poor';
    summary.recommendations.push('❌ 表示に問題があります。IPFS伝播を待つか、代替手段を検討してください');
  } else {
    summary.overallStatus = 'failed';
    summary.recommendations.push('🚨 重大な問題があります。コントラクトやメタデータを確認してください');
  }

  // 具体的な推奨事項
  if (summary.issues.some(issue => issue.step === 2)) {
    summary.recommendations.push('💡 メタデータが取得できません。IPFSの伝播を待つか、異なるゲートウェイを試してください');
  }

  if (summary.issues.some(issue => issue.step === 3)) {
    summary.recommendations.push('💡 画像ファイルにアクセスできません。時間をおいて再確認するか、代替URLを使用してください');
  }

  return summary;
};

// ブラウザコンソール用の簡易診断関数
if (typeof window !== 'undefined') {
  window.debugEtherscanNFT = async (contractAddress, tokenId) => {
    const report = await generateEtherscanDiagnosticReport(contractAddress, tokenId);

    console.log('\n🎯 診断結果サマリー');
    console.log('================================');
    console.log(`📊 総合ステータス: ${report.summary.overallStatus}`);
    console.log(`✅ 成功したステップ: ${report.summary.successfulSteps}/${report.summary.totalSteps}`);

    console.log('\n🔧 推奨事項:');
    report.summary.recommendations.forEach(rec => {
      console.log(`  ${rec}`);
    });

    if (report.summary.issues.length > 0) {
      console.log('\n⚠️ 発見された問題:');
      report.summary.issues.forEach(issue => {
        console.log(`  Step ${issue.step}: ${issue.description} - ${issue.error}`);
      });
    }

    return report;
  };
}

export default {
  generateEtherscanDiagnosticReport
};
