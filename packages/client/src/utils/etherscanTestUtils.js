/**
 * Etherscan表示テスト用ユーティリティ
 *
 * このファイルはEtherscan表示の確認を手動で行うためのヘルパー関数を提供します。
 * ブラウザのコンソールから実行して、NFTの表示状況を確認できます。
 */

// 手動テスト用のヘルパー関数をwindowオブジェクトに追加
if (typeof window !== 'undefined') {
  // Etherscan表示テスト関数
  window.testEtherscanNFT = async (contractAddress, tokenId) => {
    try {
      console.log('🔍 Etherscan NFT表示テスト開始');
      console.log('==================================');
      console.log(`📋 Contract: ${contractAddress}`);
      console.log(`🏷️ Token ID: ${tokenId}`);

      // ethersが利用可能かチェック
      if (typeof window.ethers === 'undefined') {
        console.log('❌ ethersライブラリが見つかりません');
        console.log('💡 ページが完全に読み込まれてから再度お試しください');
        return;
      }

      // ウォレット接続チェック
      if (typeof window.ethereum === 'undefined') {
        console.log('❌ Ethereumウォレットが見つかりません');
        return;
      }

      const provider = new window.ethers.BrowserProvider(window.ethereum);

      // 最小限のERC-721 ABI
      const minimalERC721ABI = [
        "function tokenURI(uint256 tokenId) external view returns (string memory)",
        "function ownerOf(uint256 tokenId) external view returns (address)",
        "function name() external view returns (string memory)"
      ];

      const contract = new window.ethers.Contract(contractAddress, minimalERC721ABI, provider);

      // 1. NFTの存在確認
      console.log('\n1️⃣ NFTの存在確認...');
      let owner;
      try {
        owner = await contract.ownerOf(tokenId);
        console.log(`✅ NFTが存在します (所有者: ${owner})`);
      } catch (error) {
        console.log('❌ NFTが存在しないか、無効なTokenIDです');
        console.log('Error:', error.message);
        return;
      }

      // 2. メタデータURI取得
      console.log('\n2️⃣ メタデータURI取得...');
      const tokenURI = await contract.tokenURI(tokenId);
      console.log(`📄 Token URI: ${tokenURI}`);

      // 3. メタデータ取得・解析
      console.log('\n3️⃣ メタデータ解析...');
      try {
        const response = await fetch(tokenURI);
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const metadata = await response.json();
        console.log('✅ メタデータ取得成功');
        console.log('📄 メタデータ内容:');
        console.log(JSON.stringify(metadata, null, 2));

        // 4. 画像アクセシビリティテスト
        if (metadata.image) {
          console.log('\n4️⃣ 画像アクセシビリティテスト...');
          console.log(`🖼️ 画像URL: ${metadata.image}`);

          try {
            const imageResponse = await fetch(metadata.image, { method: 'HEAD' });
            if (imageResponse.ok) {
              console.log('✅ 画像アクセス成功');
              console.log(`📏 サイズ: ${imageResponse.headers.get('content-length')} bytes`);
              console.log(`📄 タイプ: ${imageResponse.headers.get('content-type')}`);

              // 5. Etherscan互換性チェック
              console.log('\n5️⃣ Etherscan互換性チェック...');
              const isHttps = metadata.image.startsWith('https://');
              const hasName = !!metadata.name;
              const hasDescription = !!metadata.description;

              console.log(`🔗 HTTPS形式: ${isHttps ? '✅' : '❌'}`);
              console.log(`🏷️ 名前: ${hasName ? '✅' : '❌'} ${metadata.name || 'なし'}`);
              console.log(`📝 説明: ${hasDescription ? '✅' : '❌'} ${metadata.description || 'なし'}`);

              const isCompatible = isHttps && hasName && hasDescription;
              console.log(`\n🎯 Etherscan表示互換性: ${isCompatible ? '✅ 合格' : '❌ 不合格'}`);

              // 6. 画像プレビュー
              console.log('\n6️⃣ 画像プレビュー');
              console.log(`🔗 画像を新しいタブで開く: window.open('${metadata.image}', '_blank')`);

              return {
                success: true,
                compatible: isCompatible,
                metadata,
                imageUrl: metadata.image,
                owner
              };

            } else {
              console.log(`❌ 画像アクセス失敗: ${imageResponse.status}`);
            }
          } catch (imgError) {
            console.log(`❌ 画像アクセスエラー: ${imgError.message}`);
          }
        } else {
          console.log('❌ 画像URLが見つかりません');
        }

      } catch (metaError) {
        console.log(`❌ メタデータ取得失敗: ${metaError.message}`);
        console.log('💡 IPFS伝播に時間がかかっている可能性があります');
      }

    } catch (error) {
      console.log(`❌ テスト実行エラー: ${error.message}`);
    }

    console.log('\n==================================');
    console.log('🔍 テスト完了');
  };

  // 複数のIPFSゲートウェイでテストする関数
  window.testIPFSGateways = async (cid) => {
    const gateways = {
      'IPFS.io': `https://ipfs.io/ipfs/${cid}`,
      'W3S.link': `https://w3s.link/ipfs/${cid}`,
      'Dweb.link': `https://dweb.link/ipfs/${cid}`,
      'CF-IPFS': `https://cf-ipfs.com/ipfs/${cid}`,
      'Pinata': `https://gateway.pinata.cloud/ipfs/${cid}`
    };

    console.log(`🔍 IPFS CID ${cid} のゲートウェイテスト`);
    console.log('=====================================');

    for (const [name, url] of Object.entries(gateways)) {
      try {
        console.log(`⏳ ${name}をテスト中...`);
        const start = Date.now();
        const response = await fetch(url, {
          method: 'HEAD',
          signal: AbortSignal.timeout(10000) // 10秒タイムアウト
        });
        const time = Date.now() - start;

        if (response.ok) {
          console.log(`✅ ${name}: アクセス成功 (${time}ms)`);
        } else {
          console.log(`❌ ${name}: HTTP ${response.status} (${time}ms)`);
        }
      } catch (error) {
        console.log(`❌ ${name}: ${error.message}`);
      }
    }

    console.log('=====================================');
    console.log('🔍 ゲートウェイテスト完了');
  };

  // ヘルプ関数
  window.etherscanTestHelp = () => {
    console.log('🎯 Etherscan NFT表示テスト用コマンド');
    console.log('=====================================');
    console.log('');
    console.log('📋 基本的な使い方:');
    console.log('testEtherscanNFT("CONTRACT_ADDRESS", TOKEN_ID)');
    console.log('');
    console.log('🔍 例:');
    console.log('testEtherscanNFT("******************************************", 10)');
    console.log('');
    console.log('🌐 IPFSゲートウェイテスト:');
    console.log('testIPFSGateways("YOUR_IPFS_CID")');
    console.log('');
    console.log('💡 ヒント:');
    console.log('・NFT作成後、数分待ってからテストしてください');
    console.log('・IPFS伝播には時間がかかる場合があります');
    console.log('・エラーが出る場合は、しばらく時間をおいて再実行してください');
    console.log('');
    console.log('=====================================');
  };

  // 初回ヘルプ表示
  console.log('🎯 Etherscan NFTテストユーティリティが読み込まれました！');
  console.log('💡 使い方を確認するには: etherscanTestHelp()');
}

// React開発環境での警告抑制
if (process.env.NODE_ENV === 'development') {
  console.log('🔧 開発モード: Etherscanテストユーティリティが利用可能です');
}

export { }; // ESモジュールとして扱う

