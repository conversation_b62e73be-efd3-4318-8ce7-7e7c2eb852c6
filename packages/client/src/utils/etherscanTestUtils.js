/**\n * Etherscan表示テスト用ユーティリティ\n * \n * このファイルはEtherscan表示の確認を手動で行うためのヘルパー関数を提供します。\n * ブラウザのコンソールから実行して、NFTの表示状況を確認できます。\n */\n\n// 手動テスト用のヘルパー関数をwindowオブジェクトに追加\nif (typeof window !== 'undefined') {\n  // Etherscan表示テスト関数\n  window.testEtherscanNFT = async (contractAddress, tokenId) => {\n    try {\n      console.log('🔍 Etherscan NFT表示テスト開始');\n      console.log('==================================');\n      console.log(`📋 Contract: ${contractAddress}`);\n      console.log(`🏷️ Token ID: ${tokenId}`);\n      \n      // ethersが利用可能かチェック\n      if (typeof window.ethers === 'undefined') {\n        console.log('❌ ethersライブラリが見つかりません');\n        console.log('💡 ページが完全に読み込まれてから再度お試しください');\n        return;\n      }\n      \n      // ウォレット接続チェック\n      if (typeof window.ethereum === 'undefined') {\n        console.log('❌ Ethereumウォレットが見つかりません');\n        return;\n      }\n      \n      const provider = new window.ethers.BrowserProvider(window.ethereum);\n      \n      // 最小限のERC-721 ABI\n      const minimalERC721ABI = [\n        \"function tokenURI(uint256 tokenId) external view returns (string memory)\",\n        \"function ownerOf(uint256 tokenId) external view returns (address)\",\n        \"function name() external view returns (string memory)\"\n      ];\n      \n      const contract = new window.ethers.Contract(contractAddress, minimalERC721ABI, provider);\n      \n      // 1. NFTの存在確認\n      console.log('\\n1️⃣ NFTの存在確認...');\n      try {\n        const owner = await contract.ownerOf(tokenId);\n        console.log(`✅ NFTが存在します (所有者: ${owner})`);\n      } catch (error) {\n        console.log('❌ NFTが存在しないか、無効なTokenIDです');\n        console.log('Error:', error.message);\n        return;\n      }\n      \n      // 2. メタデータURI取得\n      console.log('\\n2️⃣ メタデータURI取得...');\n      const tokenURI = await contract.tokenURI(tokenId);\n      console.log(`📄 Token URI: ${tokenURI}`);\n      \n      // 3. メタデータ取得・解析\n      console.log('\\n3️⃣ メタデータ解析...');\n      try {\n        const response = await fetch(tokenURI);\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        \n        const metadata = await response.json();\n        console.log('✅ メタデータ取得成功');\n        console.log('📄 メタデータ内容:');\n        console.log(JSON.stringify(metadata, null, 2));\n        \n        // 4. 画像アクセシビリティテスト\n        if (metadata.image) {\n          console.log('\\n4️⃣ 画像アクセシビリティテスト...');\n          console.log(`🖼️ 画像URL: ${metadata.image}`);\n          \n          try {\n            const imageResponse = await fetch(metadata.image, { method: 'HEAD' });\n            if (imageResponse.ok) {\n              console.log('✅ 画像アクセス成功');\n              console.log(`📏 サイズ: ${imageResponse.headers.get('content-length')} bytes`);\n              console.log(`📄 タイプ: ${imageResponse.headers.get('content-type')}`);\n              \n              // 5. Etherscan互換性チェック\n              console.log('\\n5️⃣ Etherscan互換性チェック...');\n              const isHttps = metadata.image.startsWith('https://');\n              const hasName = !!metadata.name;\n              const hasDescription = !!metadata.description;\n              \n              console.log(`🔗 HTTPS形式: ${isHttps ? '✅' : '❌'}`);\n              console.log(`🏷️ 名前: ${hasName ? '✅' : '❌'} ${metadata.name || 'なし'}`);\n              console.log(`📝 説明: ${hasDescription ? '✅' : '❌'} ${metadata.description || 'なし'}`);\n              \n              const isCompatible = isHttps && hasName && hasDescription;\n              console.log(`\\n🎯 Etherscan表示互換性: ${isCompatible ? '✅ 合格' : '❌ 不合格'}`);\n              \n              // 6. 画像プレビュー\n              console.log('\\n6️⃣ 画像プレビュー');\n              console.log(`🔗 画像を新しいタブで開く: window.open('${metadata.image}', '_blank')`);\n              \n              return {\n                success: true,\n                compatible: isCompatible,\n                metadata,\n                imageUrl: metadata.image,\n                owner\n              };\n              \n            } else {\n              console.log(`❌ 画像アクセス失敗: ${imageResponse.status}`);\n            }\n          } catch (imgError) {\n            console.log(`❌ 画像アクセスエラー: ${imgError.message}`);\n          }\n        } else {\n          console.log('❌ 画像URLが見つかりません');\n        }\n        \n      } catch (metaError) {\n        console.log(`❌ メタデータ取得失敗: ${metaError.message}`);\n        console.log('💡 IPFS伝播に時間がかかっている可能性があります');\n      }\n      \n    } catch (error) {\n      console.log(`❌ テスト実行エラー: ${error.message}`);\n    }\n    \n    console.log('\\n==================================');\n    console.log('🔍 テスト完了');\n  };\n  \n  // 複数のIPFSゲートウェイでテストする関数\n  window.testIPFSGateways = async (cid) => {\n    const gateways = {\n      'IPFS.io': `https://ipfs.io/ipfs/${cid}`,\n      'W3S.link': `https://w3s.link/ipfs/${cid}`,\n      'Dweb.link': `https://dweb.link/ipfs/${cid}`,\n      'CF-IPFS': `https://cf-ipfs.com/ipfs/${cid}`,\n      'Pinata': `https://gateway.pinata.cloud/ipfs/${cid}`\n    };\n    \n    console.log(`🔍 IPFS CID ${cid} のゲートウェイテスト`);\n    console.log('=====================================');\n    \n    for (const [name, url] of Object.entries(gateways)) {\n      try {\n        console.log(`⏳ ${name}をテスト中...`);\n        const start = Date.now();\n        const response = await fetch(url, { \n          method: 'HEAD',\n          signal: AbortSignal.timeout(10000) // 10秒タイムアウト\n        });\n        const time = Date.now() - start;\n        \n        if (response.ok) {\n          console.log(`✅ ${name}: アクセス成功 (${time}ms)`);\n        } else {\n          console.log(`❌ ${name}: HTTP ${response.status} (${time}ms)`);\n        }\n      } catch (error) {\n        console.log(`❌ ${name}: ${error.message}`);\n      }\n    }\n    \n    console.log('=====================================');\n    console.log('🔍 ゲートウェイテスト完了');\n  };\n  \n  // ヘルプ関数\n  window.etherscanTestHelp = () => {\n    console.log('🎯 Etherscan NFT表示テスト用コマンド');\n    console.log('=====================================');\n    console.log('');\n    console.log('📋 基本的な使い方:');\n    console.log('testEtherscanNFT(\"CONTRACT_ADDRESS\", TOKEN_ID)');\n    console.log('');\n    console.log('🔍 例:');\n    console.log('testEtherscanNFT(\"******************************************\", 10)');\n    console.log('');\n    console.log('🌐 IPFSゲートウェイテスト:');\n    console.log('testIPFSGateways(\"YOUR_IPFS_CID\")');\n    console.log('');\n    console.log('💡 ヒント:');\n    console.log('・NFT作成後、数分待ってからテストしてください');\n    console.log('・IPFS伝播には時間がかかる場合があります');\n    console.log('・エラーが出る場合は、しばらく時間をおいて再実行してください');\n    console.log('');\n    console.log('=====================================');\n  };\n  \n  // 初回ヘルプ表示\n  console.log('🎯 Etherscan NFTテストユーティリティが読み込まれました！');\n  console.log('💡 使い方を確認するには: etherscanTestHelp()');\n}\n\n// React開発環境での警告抑制\nif (process.env.NODE_ENV === 'development') {\n  console.log('🔧 開発モード: Etherscanテストユーティリティが利用可能です');\n}\n\nexport {}; // ESモジュールとして扱う