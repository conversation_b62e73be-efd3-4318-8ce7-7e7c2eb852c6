import { Web3Storage } from 'web3.storage';

/**
 * Web3.Storage クライアントを取得
 */
const getWeb3StorageClient = () => {
  const token = process.env.REACT_APP_WEB3_STORAGE_TOKEN;
  
  if (!token) {
    throw new Error(
      'Web3.Storage API token not found. Please set REACT_APP_WEB3_STORAGE_TOKEN in your .env file.\n' +
      'Get your free token at: https://web3.storage/'
    );
  }
  
  return new Web3Storage({ token });
};

/**
 * 単一ファイルをWeb3.Storageにアップロード
 */
export const uploadToIPFS = async (file) => {
  try {
    console.log(`🌐 Uploading ${file.name} to Web3.Storage...`);
    
    const client = getWeb3StorageClient();
    
    // ファイルをアップロード
    const cid = await client.put([file], {
      name: `nft-image-${Date.now()}`,
      maxRetries: 3
    });
    
    console.log(`✅ File uploaded successfully!`);
    console.log(`📸 Image CID: ${cid}`);
    console.log(`🔗 IPFS URI: ipfs://${cid}/${file.name}`);
    console.log(`🌍 HTTPS URL: https://${cid}.ipfs.w3s.link/${file.name}`);
    
    return cid;
  } catch (error) {
    console.error('❌ Web3.Storage upload error:', error);
    throw new Error(`IPFS upload failed: ${error.message}`);
  }
};

/**
 * メタデータをWeb3.Storageにアップロード
 */
export const uploadMetadata = async (metadata) => {
  try {
    console.log('🌐 Uploading metadata to Web3.Storage...');
    
    const client = getWeb3StorageClient();
    
    // メタデータをJSONファイルとして作成
    const metadataBlob = new Blob([JSON.stringify(metadata, null, 2)], {
      type: 'application/json'
    });
    
    const metadataFile = new File([metadataBlob], 'metadata.json', {
      type: 'application/json'
    });
    
    // メタデータをアップロード
    const cid = await client.put([metadataFile], {
      name: `nft-metadata-${Date.now()}`,
      maxRetries: 3
    });
    
    console.log(`✅ Metadata uploaded successfully!`);
    console.log(`📄 Metadata CID: ${cid}`);
    console.log(`🔗 IPFS URI: ipfs://${cid}/metadata.json`);
    console.log(`🌍 HTTPS URL: https://${cid}.ipfs.w3s.link/metadata.json`);
    
    return cid;
  } catch (error) {
    console.error('❌ Web3.Storage metadata upload error:', error);
    throw new Error(`Metadata upload failed: ${error.message}`);
  }
};

/**
 * NFTデータ一括アップロード（画像 + メタデータ）
 */
export const uploadNFTData = async (imageFile, name, description) => {
  try {
    console.log('🚀 Starting NFT data upload to Web3.Storage...');
    console.log(`📁 Image: ${imageFile.name} (${Math.round(imageFile.size / 1024)} KB)`);
    
    // 1. 画像をアップロード
    const imageCID = await uploadToIPFS(imageFile);
    const imageURI = `ipfs://${imageCID}/${imageFile.name}`;
    
    // 2. メタデータ作成
    const metadata = {
      name,
      description,
      image: imageURI,
      external_url: `https://${imageCID}.ipfs.w3s.link/${imageFile.name}`,
      attributes: [
        {
          trait_type: "Upload Date",
          value: new Date().toISOString().split('T')[0]
        },
        {
          trait_type: "File Type",
          value: imageFile.type
        },
        {
          trait_type: "File Size (KB)",
          value: Math.round(imageFile.size / 1024)
        },
        {
          trait_type: "Upload Timestamp",
          value: Date.now()
        },
        {
          trait_type: "Storage Provider",
          value: "Web3.Storage (IPFS)"
        }
      ]
    };
    
    console.log('📄 Generated metadata:', metadata);
    
    // 3. メタデータをアップロード
    const metadataCID = await uploadMetadata(metadata);
    const metadataURI = `ipfs://${metadataCID}/metadata.json`;
    
    console.log('🎉 NFT data upload completed!');
    console.log('📸 Image URI:', imageURI);
    console.log('📄 Metadata URI:', metadataURI);
    console.log('🌍 Image HTTPS:', `https://${imageCID}.ipfs.w3s.link/${imageFile.name}`);
    console.log('🌍 Metadata HTTPS:', `https://${metadataCID}.ipfs.w3s.link/metadata.json`);
    
    return metadataURI;
  } catch (error) {
    console.error('❌ NFT data upload error:', error);
    throw error;
  }
};

/**
 * IPFS CIDからHTTPS URLを生成
 */
export const getHttpsUrl = (ipfsUri) => {
  if (!ipfsUri || !ipfsUri.startsWith('ipfs://')) {
    return ipfsUri;
  }
  
  const hash = ipfsUri.replace('ipfs://', '');
  const [cid, ...pathParts] = hash.split('/');
  const path = pathParts.length > 0 ? `/${pathParts.join('/')}` : '';
  
  return `https://${cid}.ipfs.w3s.link${path}`;
};

/**
 * Web3.Storage接続テスト
 */
export const testWeb3StorageConnection = async () => {
  try {
    const client = getWeb3StorageClient();
    
    // テストファイルを作成
    const testContent = `Web3.Storage connection test - ${new Date().toISOString()}`;
    const testBlob = new Blob([testContent], { type: 'text/plain' });
    const testFile = new File([testBlob], 'connection-test.txt', { type: 'text/plain' });
    
    console.log('🧪 Testing Web3.Storage connection...');
    const cid = await client.put([testFile], {
      name: 'connection-test',
      maxRetries: 1
    });
    
    console.log('✅ Web3.Storage connection successful!');
    console.log('🔗 Test file CID:', cid);
    return true;
  } catch (error) {
    console.error('❌ Web3.Storage connection failed:', error);
    return false;
  }
};
