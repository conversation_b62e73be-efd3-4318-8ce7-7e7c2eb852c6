/**
 * 🧪 w3up IPFS 直接テスト用スクリプト
 * 
 * 【このスクリプトの目的】
 * - w3up（Web3.Storage v2）サービスの動作確認
 * - 実際のIPFSアップロードのテスト
 * - アップロードしたファイルのアクセシビリティ確認
 * - 複数のIPFSゲートウェイでのアクセステスト
 * 
 * 【w3up（Web3.Storage v2）とは】
 * - Storacha社が提供する分散ストレージサービス
 * - IPFSをベースとした高速・信頼性の高いファイル保存
 * - 無料で利用可能（一定のストレージ容量まで）
 * - 前身はWeb3.Storage（v1）
 * 
 * 【使用条件】
 * - .envファイルにREACT_APP_W3UP_EMAILが設定されていること
 * - メール認証が完了していること
 * 
 * ブラウザコンソールで実行してw3upの動作を確認
 */

async function testW3upDirect() {
  console.log('🧪 w3up 直接テスト開始...');
  
  try {
    // w3up-client をインポート
    const Client = await import('@web3-storage/w3up-client');
    console.log('✅ w3up-client インポート成功');
    
    // クライアント作成
    const client = await Client.create();
    console.log('✅ クライアント作成成功');
    console.log('アカウント数:', Object.keys(client.accounts()).length);
    
    // テスト用小さなファイルを作成
    const testContent = JSON.stringify({
      test: true,
      timestamp: Date.now(),
      message: "w3up test file"
    }, null, 2);
    
    const testFile = new File([testContent], 'test.json', {
      type: 'application/json'
    });
    
    console.log('📤 テストファイルアップロード開始...');
    console.log('ファイルサイズ:', testFile.size, 'bytes');
    
    // アップロード実行
    const cid = await client.uploadFile(testFile);
    console.log('✅ アップロード成功!');
    console.log('CID:', cid.toString());
    
    // 複数のゲートウェイでテスト
    const gateways = [
      `https://${cid}.ipfs.w3s.link`,
      `https://ipfs.io/ipfs/${cid}`,
      `https://cloudflare-ipfs.com/ipfs/${cid}`,
      `https://gateway.pinata.cloud/ipfs/${cid}`
    ];
    
    console.log('🔍 ゲートウェイアクセステスト開始...');
    
    for (const gateway of gateways) {
      try {
        console.log(`Testing: ${gateway}`);
        const response = await fetch(gateway, { 
          method: 'HEAD',
          timeout: 5000 
        });
        
        if (response.ok) {
          console.log(`✅ ${gateway} - アクセス成功`);
          // 実際にコンテンツも取得してみる
          const contentResponse = await fetch(gateway);
          const content = await contentResponse.text();
          console.log(`📄 Content preview: ${content.substring(0, 100)}...`);
          break; // 一つでも成功したら終了
        } else {
          console.log(`❌ ${gateway} - ${response.status}`);
        }
      } catch (error) {
        console.log(`❌ ${gateway} - Error: ${error.message}`);
      }
    }
    
    console.log('🏁 w3up直接テスト完了');
    return {
      success: true,
      cid: cid.toString(),
      testUrl: `https://${cid}.ipfs.w3s.link`
    };
    
  } catch (error) {
    console.error('❌ w3up直接テスト失敗:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 実行用
console.log('💡 使用方法: testW3upDirect() を実行してください');
console.log('📝 このテストでw3upが正常に動作するか確認できます');

// 自動実行（コメントアウト可能）
// testW3upDirect();