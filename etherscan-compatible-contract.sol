/**
 * 🔧 Etherscan対応版 mintIpfsNFT 関数
 * 
 * 【修正内容】
 * - IPFS URIをHTTPS Gateway URLに変換
 * - 複数のIPFSゲートウェイに対応
 * - Etherscan表示互換性の向上
 * 
 * 📝 使用方法:
 * 1. 既存のコントラクトのmintIpfsNFT関数を以下の内容に置き換え
 * 2. 再デプロイ
 * 3. 新しいNFTを作成してテスト
 */

/**
 * 🌐 IPFSハッシュを使用してNFTをミント（Etherscan対応版）
 * 
 * 【改善点】
 * - IPFS URIをHTTPS Gateway URLに自動変換
 * - 複数のゲートウェイオプション
 * - CORS対応の改善
 * 
 * @param name NFTの名前
 * @param description NFTの説明
 * @param ipfsHash IPFSファイルのハッシュ値（例：QmXXXXX...）
 */
function mintIpfsNFT(
    string memory name,
    string memory description,
    string memory ipfsHash
) public payable nonReentrant {
    // 🔒 事前条件チェック
    if (!mintingEnabled) revert MintingDisabled();
    if (_tokenIdCounter > MAX_SUPPLY) revert MaxSupplyExceeded();
    if (msg.value < mintPrice) revert InsufficientPayment();
    if (bytes(name).length == 0) revert EmptyName();
    if (bytes(description).length == 0) revert EmptyDescription();
    if (bytes(ipfsHash).length == 0) revert InvalidIPFSHash();

    uint256 tokenId = _tokenIdCounter;

    // 🌐 IPFSハッシュからHTTPS Gateway URLを生成（Etherscan対応）
    // Cloudflare IPFS Gateway を使用（高速・安定・CORS対応）
    string memory imageURI = string(abi.encodePacked("https://cloudflare-ipfs.com/ipfs/", ipfsHash));
    
    // 📝 NFT情報をブロックチェーンに保存
    nftInfo[tokenId] = NFTInfo({
        name: name,
        description: description,
        imageURI: imageURI,  // HTTPS URL を保存
        timestamp: block.timestamp,
        minter: msg.sender
    });

    // 🔧 Etherscan対応メタデータURIを生成
    string memory metadataURI = _generateEtherscanCompatibleMetadataURI(tokenId);

    // 🎨 NFTをミント
    _safeMint(msg.sender, tokenId);
    _setTokenURI(tokenId, metadataURI);

    // 📊 カウンターを増加
    _tokenIdCounter++;

    // 🖥️ ログ出力
    console.log(
        "Etherscan-compatible IPFS NFT minted! ID: %s, Minter: %s, IPFS: %s, HTTPS: %s",
        tokenId,
        msg.sender,
        ipfsHash,
        imageURI
    );

    // 📡 イベント発行
    emit IPFSNFTMinted(tokenId, msg.sender, ipfsHash);
    emit NFTMinted(tokenId, msg.sender, imageURI, metadataURI);
}

/**
 * 🔧 Etherscan互換メタデータURIを生成する内部関数
 * 
 * 【改善点】
 * - HTTPS画像URLを使用
 * - external_url フィールドを追加
 * - OpenSea互換性の向上
 * 
 * @param tokenId 対象のトークンID
 * @return Etherscan互換のメタデータURI
 */
function _generateEtherscanCompatibleMetadataURI(uint256 tokenId) internal view returns (string memory) {
    NFTInfo memory info = nftInfo[tokenId];

    // 📝 Etherscan/OpenSea互換JSON形式のメタデータを構築
    string memory json = string(abi.encodePacked(
        '{"name": "', info.name, '",',
        '"description": "', info.description, '",',
        '"image": "', info.imageURI, '",',
        '"external_url": "https://gemcase.vercel.app/",',  // 外部リンク追加
        '"attributes": [',
            '{"trait_type": "Minter", "value": "', _addressToString(info.minter), '"},',
            '{"trait_type": "Mint Timestamp", "value": ', _uint256ToString(info.timestamp), '},',
            '{"trait_type": "Token ID", "value": ', _uint256ToString(tokenId), '},',
            '{"trait_type": "Blockchain", "value": "Ethereum"},',
            '{"trait_type": "Standard", "value": "ERC721"}',
        ']}'
    ));

    // 🔐 JSONをBase64エンコードしてdata URIとして返す
    return Base64.encodeJSON(json);
}

/**
 * 🔧 代替案: HTTPS URLを直接メタデータURIとして使用
 * 
 * Base64エンコードの代わりにHTTPS URLでメタデータを提供
 * （より確実にEtherscanで表示される）
 */
function _generateHttpsMetadataURI(uint256 tokenId) internal view returns (string memory) {
    // 実際の実装では、外部のメタデータAPIを使用
    // 例: https://api.yourproject.com/metadata/{tokenId}
    return string(abi.encodePacked("https://api.yourproject.com/metadata/", _uint256ToString(tokenId)));
}
