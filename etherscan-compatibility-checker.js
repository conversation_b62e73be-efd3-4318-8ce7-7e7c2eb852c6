/**
 * 🔍 Etherscan表示互換性チェッカー
 * 
 * 【このスクリプトの目的】
 * Etherscanで画像が表示されない問題を詳細に診断し、
 * OpenSeaやその他のNFTマーケットプレイスとの互換性を確認
 * 
 * 【よくある問題】
 * 1. メタデータのJSON構造がERC721規格に準拠していない
 * 2. CORS（Cross-Origin Resource Sharing）の問題
 * 3. IPFSゲートウェイのHTTPS要件
 * 4. Etherscan独自の表示要件
 * 5. Base64エンコードされたdata URIの問題
 * 
 * 使用方法: ブラウザコンソールでこのスクリプトを実行
 */

/**
 * 🔧 Etherscan互換性チェック関数
 * @param {string} contractAddress - コントラクトアドレス
 * @param {number} tokenId - トークンID
 */
async function checkEtherscanCompatibility(contractAddress, tokenId) {
  console.log('🔍 === Etherscan表示互換性チェック開始 ===');
  console.log(`📋 Contract: ${contractAddress}`);
  console.log(`🏷️ Token ID: ${tokenId}`);
  console.log('⏰ チェック開始時刻:', new Date().toLocaleString());
  
  const results = {
    tokenUri: null,
    metadata: null,
    imageUrl: null,
    compatibility: {
      etherscan: false,
      opensea: false,
      erc721: false,
      cors: false,
      https: false
    },
    issues: [],
    recommendations: []
  };
  
  try {
    // Ethereum Provider Setup
    if (!window.ethereum) {
      throw new Error('❌ MetaMask not found. Please install MetaMask.');
    }
    
    const provider = new ethers.BrowserProvider(window.ethereum);
    
    // Contract Setup
    const minimalABI = [
      "function tokenURI(uint256 tokenId) view returns (string)",
      "function name() view returns (string)",
      "function symbol() view returns (string)"
    ];
    
    const contract = new ethers.Contract(contractAddress, minimalABI, provider);
    
    // === STEP 1: コントラクト基本情報 ===
    console.log('\n📋 STEP 1: コントラクト基本情報チェック');
    try {
      const [name, symbol] = await Promise.all([
        contract.name(),
        contract.symbol()
      ]);
      console.log(`✅ Contract Name: ${name}`);
      console.log(`✅ Contract Symbol: ${symbol}`);
    } catch (error) {
      console.log('⚠️ コントラクト基本情報の取得に失敗:', error.message);
      results.issues.push('Contract basic info retrieval failed');
    }
    
    // === STEP 2: TokenURI取得 ===
    console.log('\n📄 STEP 2: TokenURI取得・検証');
    try {
      const tokenURI = await contract.tokenURI(tokenId);
      results.tokenUri = tokenURI;
      console.log(`✅ TokenURI: ${tokenURI}`);
      
      // TokenURI形式チェック
      if (tokenURI.startsWith('data:application/json;base64,')) {
        console.log('📦 Base64エンコードされたJSON形式');
        results.compatibility.erc721 = true;
      } else if (tokenURI.startsWith('https://')) {
        console.log('🌐 HTTPS URL形式');
        results.compatibility.https = true;
      } else if (tokenURI.startsWith('http://')) {
        console.log('⚠️ HTTP URL形式 (Etherscanでは表示されない可能性)');
        results.issues.push('HTTP URLs may not display in Etherscan');
        results.recommendations.push('Use HTTPS URLs for better compatibility');
      } else if (tokenURI.startsWith('ipfs://')) {
        console.log('🌍 IPFS URI形式');
        results.issues.push('Raw IPFS URIs may not display in Etherscan');
        results.recommendations.push('Use HTTPS IPFS gateway URLs');
      }
      
    } catch (error) {
      console.log('❌ TokenURI取得失敗:', error.message);
      results.issues.push(`TokenURI retrieval failed: ${error.message}`);
      return results;
    }
    
    // === STEP 3: メタデータ取得・検証 ===
    console.log('\n🔍 STEP 3: メタデータ構造検証');
    try {
      let metadata;
      
      if (results.tokenUri.startsWith('data:application/json;base64,')) {
        // Base64デコード
        const base64Data = results.tokenUri.split(',')[1];
        const jsonString = atob(base64Data);
        metadata = JSON.parse(jsonString);
        console.log('✅ Base64メタデータデコード成功');
      } else if (results.tokenUri.startsWith('http')) {
        // HTTP(S)からフェッチ
        const response = await fetch(results.tokenUri);
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        metadata = await response.json();
        console.log('✅ HTTPメタデータ取得成功');
      } else {
        throw new Error('Unsupported TokenURI format');
      }
      
      results.metadata = metadata;
      console.log('📄 メタデータ内容:');
      console.log(JSON.stringify(metadata, null, 2));
      
      // === STEP 4: ERC721メタデータ規格準拠チェック ===
      console.log('\n📏 STEP 4: ERC721メタデータ規格準拠チェック');
      
      const requiredFields = ['name', 'description', 'image'];
      const optionalFields = ['external_url', 'attributes', 'animation_url'];
      
      console.log('🔍 必須フィールドチェック:');
      requiredFields.forEach(field => {
        if (metadata[field] !== undefined) {
          console.log(`✅ ${field}: 存在`);
        } else {
          console.log(`❌ ${field}: 不在`);
          results.issues.push(`Missing required field: ${field}`);
        }
      });
      
      console.log('🔍 オプションフィールドチェック:');
      optionalFields.forEach(field => {
        if (metadata[field] !== undefined) {
          console.log(`✅ ${field}: 存在`);
        } else {
          console.log(`⚪ ${field}: 不在（オプション）`);
        }
      });
      
      if (metadata.name && metadata.description && metadata.image) {
        results.compatibility.erc721 = true;
        console.log('✅ ERC721メタデータ規格: 準拠');
      } else {
        console.log('❌ ERC721メタデータ規格: 非準拠');
      }
      
    } catch (error) {
      console.log('❌ メタデータ解析失敗:', error.message);
      results.issues.push(`Metadata parsing failed: ${error.message}`);
    }
    
    // === STEP 5: 画像URL詳細検証 ===
    if (results.metadata && results.metadata.image) {
      console.log('\n🖼️ STEP 5: 画像URL詳細検証');
      const imageUrl = results.metadata.image;
      results.imageUrl = imageUrl;
      console.log(`🔗 画像URL: ${imageUrl}`);
      
      // URL形式チェック
      if (imageUrl.startsWith('https://')) {
        console.log('✅ HTTPS画像URL: Etherscan対応');
        results.compatibility.https = true;
      } else if (imageUrl.startsWith('http://')) {
        console.log('⚠️ HTTP画像URL: Etherscanで表示されない可能性');
        results.issues.push('HTTP image URLs may not display in Etherscan');
      } else if (imageUrl.startsWith('ipfs://')) {
        console.log('🌍 IPFS画像URI: 変換が必要');
        results.issues.push('IPFS URIs need gateway conversion for Etherscan');
        
        // IPFS URIをHTTPS Gateway URLに変換
        const cid = imageUrl.replace('ipfs://', '');
        const httpsUrl = `https://ipfs.io/ipfs/${cid}`;
        console.log(`🔄 変換後HTTPS URL: ${httpsUrl}`);
        results.recommendations.push(`Use HTTPS gateway: ${httpsUrl}`);
      } else if (imageUrl.startsWith('data:image/')) {
        console.log('📦 Base64画像データ: Etherscan対応');
        results.compatibility.etherscan = true;
      }
      
      // === STEP 6: 画像アクセシビリティ・CORSテスト ===
      console.log('\n🌐 STEP 6: 画像アクセシビリティ・CORSテスト');
      
      try {
        // HEAD リクエストで画像の存在確認
        const imageResponse = await fetch(imageUrl, { 
          method: 'HEAD',
          mode: 'cors' // CORS明示的チェック
        });
        
        if (imageResponse.ok) {
          console.log('✅ 画像アクセス: 成功');
          console.log(`📊 Content-Type: ${imageResponse.headers.get('content-type')}`);
          console.log(`📊 Content-Length: ${imageResponse.headers.get('content-length')} bytes`);
          
          // CORS ヘッダーチェック
          const corsHeader = imageResponse.headers.get('access-control-allow-origin');
          if (corsHeader) {
            console.log(`✅ CORS設定: ${corsHeader}`);
            results.compatibility.cors = true;
          } else {
            console.log('⚠️ CORS設定なし（Etherscanで問題の可能性）');
            results.issues.push('No CORS headers - may cause display issues');
          }
          
        } else {
          console.log(`❌ 画像アクセス失敗: HTTP ${imageResponse.status}`);
          results.issues.push(`Image access failed: HTTP ${imageResponse.status}`);
        }
      } catch (corsError) {
        console.log(`❌ CORS/画像アクセスエラー: ${corsError.message}`);
        results.issues.push(`CORS/Image access error: ${corsError.message}`);
      }
      
      // === STEP 7: 複数IPFSゲートウェイテスト ===
      if (imageUrl.startsWith('ipfs://')) {
        console.log('\n🌍 STEP 7: 複数IPFSゲートウェイテスト');
        const cid = imageUrl.replace('ipfs://', '');
        
        const gateways = [
          { name: 'IPFS.io', url: `https://ipfs.io/ipfs/${cid}` },
          { name: 'Cloudflare', url: `https://cloudflare-ipfs.com/ipfs/${cid}` },
          { name: 'Pinata', url: `https://gateway.pinata.cloud/ipfs/${cid}` },
          { name: 'Dweb.link', url: `https://dweb.link/ipfs/${cid}` }
        ];
        
        for (const gateway of gateways) {
          try {
            console.log(`🔍 Testing ${gateway.name}: ${gateway.url}`);
            const response = await fetch(gateway.url, { 
              method: 'HEAD',
              timeout: 5000 
            });
            
            if (response.ok) {
              console.log(`✅ ${gateway.name}: 利用可能`);
              if (gateway.name === 'IPFS.io' || gateway.name === 'Cloudflare') {
                results.compatibility.etherscan = true;
              }
            } else {
              console.log(`❌ ${gateway.name}: HTTP ${response.status}`);
            }
          } catch (error) {
            console.log(`❌ ${gateway.name}: ${error.message}`);
          }
        }
      }
    }
    
    // === STEP 8: 最終診断・推奨事項 ===
    console.log('\n📊 STEP 8: 最終診断・推奨事項');
    
    // Etherscan互換性判定
    if (results.compatibility.https && results.compatibility.erc721 && results.compatibility.cors) {
      results.compatibility.etherscan = true;
      console.log('✅ Etherscan表示: 互換性あり');
    } else {
      console.log('❌ Etherscan表示: 互換性に問題あり');
    }
    
    // OpenSea互換性判定
    if (results.compatibility.erc721) {
      results.compatibility.opensea = true;
      console.log('✅ OpenSea表示: 互換性あり');
    } else {
      console.log('❌ OpenSea表示: 互換性に問題あり');
    }
    
    // 問題の要約
    if (results.issues.length > 0) {
      console.log('\n⚠️ 発見された問題:');
      results.issues.forEach((issue, index) => {
        console.log(`${index + 1}. ${issue}`);
      });
    }
    
    // 推奨事項
    if (results.recommendations.length > 0) {
      console.log('\n💡 推奨改善事項:');
      results.recommendations.forEach((rec, index) => {
        console.log(`${index + 1}. ${rec}`);
      });
    }
    
    // === 具体的な修正案 ===
    console.log('\n🔧 具体的な修正案:');
    
    if (results.imageUrl && results.imageUrl.startsWith('ipfs://')) {
      const cid = results.imageUrl.replace('ipfs://', '');
      console.log('📝 修正案1: IPFSゲートウェイURL使用');
      console.log(`現在: ${results.imageUrl}`);
      console.log(`修正後: https://ipfs.io/ipfs/${cid}`);
      console.log('→ コントラクトのmintIpfsNFT関数でHTTPS URLを使用');
    }
    
    if (!results.compatibility.cors) {
      console.log('📝 修正案2: CORS対応IPFSゲートウェイ使用');
      console.log('→ Cloudflare IPFS Gateway使用を推奨');
    }
    
    if (!results.compatibility.erc721) {
      console.log('📝 修正案3: メタデータ構造修正');
      console.log('→ name, description, imageフィールドが必須');
    }
    
  } catch (error) {
    console.error('❌ チェック処理でエラー:', error);
    results.issues.push(`Check process error: ${error.message}`);
  }
  
  console.log('\n🏁 === Etherscan互換性チェック完了 ===');
  console.log('⏰ チェック完了時刻:', new Date().toLocaleString());
  
  // 結果オブジェクトをコンソールに出力
  console.log('\n📋 チェック結果サマリー:');
  console.table(results.compatibility);
  
  return results;
}

/**
 * 🔧 自動修正提案生成
 * @param {Object} checkResults - checkEtherscanCompatibility の結果
 */
function generateFixSuggestions(checkResults) {
  console.log('\n🛠️ === 自動修正提案 ===');
  
  const fixes = [];
  
  if (checkResults.imageUrl && checkResults.imageUrl.startsWith('ipfs://')) {
    const cid = checkResults.imageUrl.replace('ipfs://', '');
    fixes.push({
      issue: 'IPFS URI format',
      fix: `Use HTTPS gateway URL: https://ipfs.io/ipfs/${cid}`,
      code: `
// コントラクト修正例:
const imageURI = "https://ipfs.io/ipfs/${cid}";
// または
const imageURI = "https://cloudflare-ipfs.com/ipfs/${cid}";
      `
    });
  }
  
  if (!checkResults.compatibility.erc721) {
    fixes.push({
      issue: 'ERC721 metadata compliance',
      fix: 'Ensure metadata has required fields',
      code: `
// 正しいメタデータ構造:
const metadata = {
  "name": "Your NFT Name",
  "description": "Your NFT Description", 
  "image": "https://ipfs.io/ipfs/YOUR_CID",
  "external_url": "https://your-website.com",
  "attributes": [
    {
      "trait_type": "Property", 
      "value": "Value"
    }
  ]
};
      `
    });
  }
  
  fixes.forEach((fix, index) => {
    console.log(`\n🔧 修正${index + 1}: ${fix.issue}`);
    console.log(`💡 解決方法: ${fix.fix}`);
    console.log(`📝 コード例:${fix.code}`);
  });
  
  return fixes;
}

// 使用例とヘルプ
console.log('🔍 === Etherscan互換性チェッカー ===');
console.log('💡 使用方法:');
console.log('1. checkEtherscanCompatibility("0xYourContractAddress", tokenId)');
console.log('2. generateFixSuggestions(結果オブジェクト)');
console.log('');
console.log('📝 例:');
console.log('const results = await checkEtherscanCompatibility("******************************************", 7);');
console.log('generateFixSuggestions(results);');

// Ethers.js自動ロード
if (typeof ethers === 'undefined') {
  console.log('📦 Ethers.jsライブラリを読み込み中...');
  const script = document.createElement('script');
  script.src = 'https://cdn.ethers.io/lib/ethers-5.7.2.umd.min.js';
  script.onload = () => {
    console.log('✅ Ethers.js読み込み完了');
    console.log('🚀 チェッカー使用準備完了!');
  };
  document.head.appendChild(script);
} else {
  console.log('🚀 チェッカー使用準備完了!');
}
