/**
 * 📋 NFTメタデータ確認用デバッグスクリプト
 * 
 * 【このスクリプトの目的】
 * - 作成したNFTのメタデータが正しく設定されているかチェック
 * - IPFS上の画像ファイルがアクセス可能かテスト
 * - 複数のIPFSゲートウェイでアクセス可能性を確認
 * - NFTマーケットプレイスで正常に表示されるかテスト
 * 
 * 【使用場面】
 * ✅ NFT作成後の動作確認
 * ✅ 画像が表示されない場合のトラブルシューティング
 * ✅ IPFSアクセス問題の診断
 * ✅ メタデータ構造の確認
 * 
 * 【初心者向け解説】
 * NFT = デジタル証明書
 * メタデータ = NFTの詳細情報（名前、画像、説明など）
 * IPFS = 分散型ファイル保存システム
 * TokenURI = メタデータの保存場所を示すアドレス
 * 
 * ブラウザコンソールで実行（F12 → Console）
 */

/**
 * 📝 使用方法（ステップバイステップ）:
 * 
 * 1. Etherscanで該当NFTページを開く
 *    例: https://sepolia.etherscan.io/address/******************************************
 * 
 * 2. ブラウザのコンソールを開く
 *    - Windows/Linux: F12 または Ctrl+Shift+I
 *    - Mac: Cmd+Option+I
 *    - 「Console」タブを選択
 * 
 * 3. 以下のコードをコピー＆ペースト
 *    - このファイル全体をコピーしてコンソールに貼り付け
 * 
 * 4. 実行コマンド:
 *    checkNFTMetadata('******************************************', 7)
 *    
 *    引数の説明:
 *    - 第1引数: コントラクトアドレス（0xから始まる42文字）
 *    - 第2引数: トークンID（数字）
 *
 * 🔍 結果の見方:
 * ✅ = 正常
 * ❌ = エラーあり
 * ⚠️ = 注意が必要
 */

/**
 * 🔍 NFTメタデータ詳細確認関数
 * 
 * 【この関数が実行する処理】
 * 1. ウォレット（MetaMask）の確認
 * 2. コントラクトへの接続
 * 3. TokenURI（メタデータの保存場所）の取得
 * 4. メタデータファイルのダウンロード
 * 5. 画像ファイルのアクセシビリティテスト
 * 6. 複数のIPFSゲートウェイでのアクセステスト
 * 
 * @param {string} contractAddress - コントラクトアドレス（0xで始まる42文字）
 * @param {number} tokenId - トークンID（NFTの固有番号）
 * 
 * 【エラーの原因と対処法】
 * - "MetaMask not found" → MetaMaskをインストールしてください
 * - "HTTP 404" → メタデータファイルが見つかりません
 * - "IPFS timeout" → IPFSゲートウェイが遅いためしばらく待ってから再試行
 */
async function checkNFTMetadata(contractAddress, tokenId) {
  console.log('🔍 NFTメタデータ詳細確認開始...');
  console.log(`Contract: ${contractAddress}`);
  console.log(`Token ID: ${tokenId}`);
  
  try {
    // 📱 Ethereum provider setup
    // MetaMaskがWeb3プロバイダーとしてインジェクトされているか確認
    if (!window.ethereum) {
      throw new Error('MetaMask not found');
    }
    
    const provider = new ethers.BrowserProvider(window.ethereum);
    
    // Contract ABI (tokenURIメソッドのみ)
    const minimalABI = [
      "function tokenURI(uint256 tokenId) view returns (string)"
    ];
    
    const contract = new ethers.Contract(contractAddress, minimalABI, provider);
    
    // 1. TokenURIを取得
    console.log('\n📋 Step 1: TokenURIを取得中...');
    const tokenURI = await contract.tokenURI(tokenId);
    console.log(`✅ TokenURI: ${tokenURI}`);
    
    // 2. メタデータを取得
    console.log('\n📄 Step 2: メタデータを取得中...');
    if (tokenURI.startsWith('http')) {
      try {
        const response = await fetch(tokenURI);
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const metadata = await response.json();
        console.log('✅ メタデータ取得成功:');
        console.log(JSON.stringify(metadata, null, 2));
        
        // 3. 画像URLをテスト
        if (metadata.image) {
          console.log('\n🖼️ Step 3: 画像URLをテスト中...');
          console.log(`画像URL: ${metadata.image}`);
          
          try {
            const imageResponse = await fetch(metadata.image, { method: 'HEAD' });
            if (imageResponse.ok) {
              console.log('✅ 画像ファイルアクセス可能');
              console.log(`Content-Type: ${imageResponse.headers.get('content-type')}`);
              console.log(`Content-Length: ${imageResponse.headers.get('content-length')} bytes`);
              
              // 画像を新しいタブで開く
              console.log('\n🔗 画像を新しいタブで開くには:');
              console.log(`window.open('${metadata.image}', '_blank');`);
              
            } else {
              console.log(`❌ 画像ファイルアクセス失敗: ${imageResponse.status}`);
            }
          } catch (imgError) {
            console.log(`❌ 画像アクセスエラー: ${imgError.message}`);
          }
        } else {
          console.log('❌ メタデータに画像URLが含まれていません');
        }
        
        // 4. IPFS Gateway代替URLをテスト
        if (metadata.image && metadata.image.startsWith('ipfs://')) {
          console.log('\n🌐 Step 4: 代替IPFSゲートウェイをテスト中...');
          const cid = metadata.image.replace('ipfs://', '');
          
          const gateways = [
            `https://ipfs.io/ipfs/${cid}`,
            `https://gateway.pinata.cloud/ipfs/${cid}`,
            `https://cloudflare-ipfs.com/ipfs/${cid}`,
            `https://dweb.link/ipfs/${cid}`
          ];
          
          for (const gateway of gateways) {
            try {
              console.log(`Testing: ${gateway}`);
              const response = await fetch(gateway, { method: 'HEAD' });
              if (response.ok) {
                console.log(`✅ ${gateway} - アクセス可能`);
              } else {
                console.log(`❌ ${gateway} - ${response.status}`);
              }
            } catch (error) {
              console.log(`❌ ${gateway} - エラー: ${error.message}`);
            }
          }
        }
        
      } catch (fetchError) {
        console.log(`❌ メタデータ取得失敗: ${fetchError.message}`);
        
        // IPFSゲートウェイが遅い場合の代替確認
        if (tokenURI.includes('ipfs')) {
          console.log('\n🔄 IPFSゲートウェイが遅い可能性があります。しばらく待ってから再試行してください。');
        }
      }
    } else {
      console.log('❌ TokenURIがHTTP URLではありません');
    }
    
  } catch (error) {
    console.log(`❌ エラーが発生しました: ${error.message}`);
  }
  
  console.log('\n🏁 メタデータ確認完了');
}

// Ethersライブラリが読み込まれていない場合のロード
if (typeof ethers === 'undefined') {
  console.log('📦 Ethersライブラリを読み込み中...');
  const script = document.createElement('script');
  script.src = 'https://cdn.ethers.io/lib/ethers-5.7.2.umd.min.js';
  script.onload = () => {
    console.log('✅ Ethersライブラリ読み込み完了');
    console.log('💡 使用方法: checkNFTMetadata("******************************************", 7)');
  };
  document.head.appendChild(script);
} else {
  console.log('💡 使用方法: checkNFTMetadata("******************************************", 7)');
}
